# backtest_stochastic_rsi_multiperiod.py
import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
import matplotlib.pyplot as plt
import pandas as pd
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime

from strategies.stochastic_rsi import fetch_stoch_rsi

# ENV
load_dotenv()
client = Client(os.getenv("BINANCE_API_KEY"), os.getenv("BINANCE_SECRET_KEY"))

# Periods
PERIODS = {
    "2022_Q1": ("2022-01-01", "2022-04-01"),
    "2023_Q2": ("2023-04-01", "2023-07-01"),
    "2024_Q3": ("2024-07-01", "2024-10-01"),
}

# Directories
LOG_DIR = "backtest_results/logs"
PLOT_DIR = "backtest_results/plots"
os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(PLOT_DIR, exist_ok=True)

# Backtest params
INITIAL_BALANCE = 1000.0
TRADE_AMOUNT = 1.0

def backtest(df):
    balance = INITIAL_BALANCE
    position = 0.0
    balance_over_time = []

    for i, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]

        if signal == "BUY" and position == 0.0:
            position = balance / price  # tüm bakiyeyle BTC al
            balance = 0.0

        elif signal == "SELL" and position > 0.0:
            balance = position * price  # tüm BTC'yi dolara çevir
            position = 0.0

        portfolio_value = balance + (position * price)
        balance_over_time.append(portfolio_value)

    final_value = balance + (position * df.iloc[-1]["close"])
    return balance_over_time, final_value


def save_plot_and_log(df, balance_over_time, final_value, label):
    max_val = max(balance_over_time)
    min_val = min(balance_over_time)

    # Plot
    plt.figure(figsize=(10, 5))
    plt.plot(df["timestamp"], balance_over_time, label="Balance Over Time")
    plt.title(f"Stochastic RSI 📈 Portfolio Value - {label}")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plot_path = os.path.join(PLOT_DIR, f"portfolio_plot_{label}_stochrsi.png")
    plt.savefig(plot_path)
    plt.close()

    # Log
    log_path = os.path.join(LOG_DIR, f"backtest_log_{label}_stochrsi.txt")
    with open(log_path, "w") as f:
        f.write(f"Final Portfolio Value: ${final_value:.2f}\n")
        f.write(f"Max Value: ${max_val:.2f}\n")
        f.write(f"Min Value: ${min_val:.2f}\n")
        f.write(f"Total Trades: {df['signal'].value_counts().to_dict()}\n")

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_val:.2f} | Min: ${min_val:.2f}")
    print(f"📁 Log saved to {log_path} | 📊 Chart saved to {plot_path}\n")

# Main
for label, (start_str, end_str) in PERIODS.items():
    print(f"🔁 Testing period: {label} ({start_str} - {end_str})")
    start = datetime.strptime(start_str, "%Y-%m-%d")
    end = datetime.strptime(end_str, "%Y-%m-%d")
    df = fetch_stoch_rsi(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=start, end_time=end)
    df.dropna(inplace=True)

    balance_over_time, final_value = backtest(df)
    save_plot_and_log(df, balance_over_time, final_value, label)
