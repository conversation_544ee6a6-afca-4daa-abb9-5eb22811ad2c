#!/usr/bin/env python3
"""
Comprehensive Trading System Optimizer
Tests multiple timeframes, parameters, and strategies to find the best profitable setup
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
import itertools
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from enhanced_backtester import EnhancedBacktester

# Import strategies
try:
    from strategies.UPDATED_rsi_macd import fetch_rsi_macd
    from strategies.UPDATED_ema_crossover import fetch_ema_crossover
    from strategies.UPDATED_bollinger import fetch_bollinger
    from strategies.supertrend import fetch_supertrend
    from strategies.stochastic_rsi import fetch_stoch_rsi
    from strategies.advanced_momentum_strategy import fetch_advanced_momentum
except ImportError as e:
    print(f"Warning: Some strategies not available: {e}")

class ComprehensiveOptimizer:
    """
    Advanced optimizer that tests everything to find profitable setups
    """
    
    def __init__(self):
        self.strategies = {
            'rsi_macd': fetch_rsi_macd,
            'ema_crossover': fetch_ema_crossover,
            'bollinger': fetch_bollinger,
            'supertrend': fetch_supertrend,
            'stochastic_rsi': fetch_stoch_rsi,
            'advanced_momentum': fetch_advanced_momentum
        }
        
        # Test configurations
        self.timeframes = ['15m', '1h', '4h', '1d']
        self.symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'BNBUSDT', 'SOLUSDT']
        self.test_periods = [30, 60, 90, 120]  # Days to test
        
        # Strategy combinations to test
        self.strategy_combinations = [
            # Single strategies
            {'bollinger': 1.0},
            {'rsi_macd': 1.0},
            {'ema_crossover': 1.0},
            {'advanced_momentum': 1.0},
            
            # Two-strategy combinations
            {'bollinger': 0.6, 'rsi_macd': 0.4},
            {'bollinger': 0.7, 'ema_crossover': 0.3},
            {'rsi_macd': 0.6, 'ema_crossover': 0.4},
            {'advanced_momentum': 0.7, 'bollinger': 0.3},
            
            # Three-strategy combinations
            {'bollinger': 0.4, 'rsi_macd': 0.4, 'ema_crossover': 0.2},
            {'advanced_momentum': 0.5, 'bollinger': 0.3, 'rsi_macd': 0.2},
            
            # Conservative mix
            {'bollinger': 0.5, 'rsi_macd': 0.5},
            
            # Aggressive mix
            {'advanced_momentum': 0.6, 'ema_crossover': 0.4}
        ]
        
        # Signal thresholds to test
        self.thresholds = [0.05, 0.08, 0.10, 0.12, 0.15, 0.18, 0.20, 0.25, 0.30]
        
        # Risk parameters to test
        self.risk_levels = [0.005, 0.01, 0.015, 0.02, 0.025]
        
    def create_ensemble_signals(self, symbol: str, interval: str, start_time: datetime, 
                               end_time: datetime, strategy_weights: Dict, threshold: float) -> pd.DataFrame:
        """
        Create ensemble signals with given parameters
        """
        strategies_data = {}
        
        # Fetch data from selected strategies
        for strategy_name, weight in strategy_weights.items():
            if weight > 0:
                try:
                    strategy_func = self.strategies[strategy_name]
                    df = strategy_func(symbol=symbol, interval=interval, 
                                     start_time=start_time, end_time=end_time)
                    strategies_data[strategy_name] = df
                except Exception as e:
                    print(f"    Warning: {strategy_name} failed: {e}")
                    continue
        
        if not strategies_data:
            return pd.DataFrame()
        
        # Merge strategies
        base_df = None
        for strategy_name, df in strategies_data.items():
            if 'signal' not in df.columns:
                df['signal'] = 'WAIT'
            if 'signal_strength' not in df.columns:
                df['signal_strength'] = 0.5
            
            cols = ['timestamp', 'close', 'signal', 'signal_strength']
            temp_df = df[cols].copy()
            temp_df = temp_df.rename(columns={
                'signal': f'signal_{strategy_name}',
                'signal_strength': f'strength_{strategy_name}'
            })
            
            if base_df is None:
                base_df = temp_df
            else:
                base_df = base_df.merge(temp_df, on=['timestamp', 'close'], how='outer')
        
        # Calculate ensemble score
        base_df['ensemble_score'] = 0.0
        
        for strategy_name, weight in strategy_weights.items():
            signal_col = f'signal_{strategy_name}'
            strength_col = f'strength_{strategy_name}'
            
            if signal_col in base_df.columns:
                signal_numeric = base_df[signal_col].map({'BUY': 1, 'SELL': -1, 'WAIT': 0})
                strength = base_df[strength_col].fillna(0.5)
                base_df['ensemble_score'] += signal_numeric * strength * weight
        
        # Generate final signals
        base_df['signal'] = 'WAIT'
        base_df.loc[base_df['ensemble_score'] > threshold, 'signal'] = 'BUY'
        base_df.loc[base_df['ensemble_score'] < -threshold, 'signal'] = 'SELL'
        
        # Calculate signal strength
        base_df['signal_strength'] = np.abs(base_df['ensemble_score'])
        base_df['signal_strength'] = np.clip(base_df['signal_strength'], 0.1, 1.0)
        
        return base_df[['timestamp', 'close', 'signal', 'signal_strength', 'ensemble_score']]
    
    def test_configuration(self, symbol: str, interval: str, days_back: int,
                          strategy_weights: Dict, threshold: float, risk_level: float) -> Dict:
        """
        Test a specific configuration
        """
        try:
            # Date range
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Generate signals
            signals_df = self.create_ensemble_signals(
                symbol, interval, start_date, end_date, strategy_weights, threshold
            )
            
            if len(signals_df) == 0:
                return {'error': 'No data generated'}
            
            # Count signals
            signal_counts = signals_df['signal'].value_counts()
            total_signals = signal_counts.get('BUY', 0) + signal_counts.get('SELL', 0)
            
            if total_signals < 3:
                return {'error': 'Too few signals', 'num_signals': total_signals}
            
            # Run backtest
            backtester = EnhancedBacktester(
                initial_capital=10000,
                maker_fee=0.001,
                taker_fee=0.001,
                slippage_factor=0.0005
            )
            
            # Modify risk manager
            backtester.risk_manager.max_risk_per_trade = risk_level
            
            results = backtester.backtest_strategy(signals_df, symbol)
            
            # Calculate optimization score
            total_return = results.get('total_return', 0)
            win_rate = results.get('win_rate', 0)
            max_drawdown = results.get('max_drawdown', 1)
            sharpe_ratio = results.get('sharpe_ratio', 0)
            num_trades = results.get('num_trades', 0)
            
            # Comprehensive scoring system
            score = (
                total_return * 200 +           # Heavy weight on returns
                win_rate * 100 +               # Win rate importance
                sharpe_ratio * 50 +            # Risk-adjusted returns
                min(num_trades / 10, 5) * 20 - # Trade frequency (capped)
                max_drawdown * 150             # Drawdown penalty
            )
            
            return {
                'total_return': total_return,
                'win_rate': win_rate,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'num_trades': num_trades,
                'score': score,
                'num_signals': total_signals,
                'profit_factor': results.get('profit_factor', 0),
                'expectancy': results.get('expectancy', 0)
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def run_comprehensive_optimization(self, max_tests: int = 200) -> Dict:
        """
        Run comprehensive optimization across all parameters
        """
        print("🚀 COMPREHENSIVE TRADING SYSTEM OPTIMIZATION")
        print("=" * 60)
        print(f"Testing up to {max_tests} configurations...")
        print(f"Timeframes: {self.timeframes}")
        print(f"Symbols: {self.symbols}")
        print(f"Strategy combinations: {len(self.strategy_combinations)}")
        
        all_results = []
        test_count = 0
        
        # Generate all combinations
        for symbol in self.symbols:
            for interval in self.timeframes:
                for days_back in self.test_periods:
                    for strategy_weights in self.strategy_combinations:
                        for threshold in self.thresholds:
                            for risk_level in self.risk_levels:
                                
                                if test_count >= max_tests:
                                    break
                                
                                test_count += 1
                                
                                print(f"\r  Progress: {test_count}/{max_tests} - "
                                      f"{symbol} {interval} {days_back}d", end="", flush=True)
                                
                                # Test configuration
                                result = self.test_configuration(
                                    symbol, interval, days_back, 
                                    strategy_weights, threshold, risk_level
                                )
                                
                                if 'error' not in result:
                                    result.update({
                                        'symbol': symbol,
                                        'interval': interval,
                                        'days_back': days_back,
                                        'strategy_weights': strategy_weights,
                                        'threshold': threshold,
                                        'risk_level': risk_level,
                                        'test_id': test_count
                                    })
                                    all_results.append(result)
                                
                                if test_count >= max_tests:
                                    break
                            if test_count >= max_tests:
                                break
                        if test_count >= max_tests:
                            break
                    if test_count >= max_tests:
                        break
                if test_count >= max_tests:
                    break
            if test_count >= max_tests:
                break
        
        print(f"\n\n✅ Optimization complete! Tested {test_count} configurations")
        print(f"📊 Valid results: {len(all_results)}")
        
        if not all_results:
            return {'error': 'No valid results found'}
        
        # Sort by score
        all_results.sort(key=lambda x: x['score'], reverse=True)
        
        # Get top results
        top_10 = all_results[:10]
        profitable_results = [r for r in all_results if r['total_return'] > 0]
        
        return {
            'all_results': all_results,
            'top_10': top_10,
            'profitable_results': profitable_results,
            'best_config': all_results[0] if all_results else None,
            'summary_stats': self._calculate_summary_stats(all_results)
        }
    
    def _calculate_summary_stats(self, results: List[Dict]) -> Dict:
        """
        Calculate summary statistics from all results
        """
        if not results:
            return {}
        
        returns = [r['total_return'] for r in results]
        win_rates = [r['win_rate'] for r in results]
        drawdowns = [r['max_drawdown'] for r in results]
        
        profitable_count = len([r for r in results if r['total_return'] > 0])
        
        return {
            'total_tests': len(results),
            'profitable_configs': profitable_count,
            'profitability_rate': profitable_count / len(results),
            'avg_return': np.mean(returns),
            'best_return': np.max(returns),
            'worst_return': np.min(returns),
            'avg_win_rate': np.mean(win_rates),
            'avg_drawdown': np.mean(drawdowns)
        }
    
    def display_results(self, optimization_results: Dict):
        """
        Display optimization results in a clear format
        """
        if 'error' in optimization_results:
            print(f"❌ Optimization failed: {optimization_results['error']}")
            return
        
        best_config = optimization_results['best_config']
        top_10 = optimization_results['top_10']
        profitable = optimization_results['profitable_results']
        stats = optimization_results['summary_stats']
        
        print("\n" + "=" * 80)
        print("🏆 OPTIMIZATION RESULTS")
        print("=" * 80)
        
        # Summary statistics
        print(f"\n📊 Summary Statistics:")
        print(f"Total configurations tested: {stats['total_tests']}")
        print(f"Profitable configurations: {stats['profitable_configs']} ({stats['profitability_rate']:.1%})")
        print(f"Average return: {stats['avg_return']:.2%}")
        print(f"Best return found: {stats['best_return']:.2%}")
        print(f"Average win rate: {stats['avg_win_rate']:.1%}")
        print(f"Average max drawdown: {stats['avg_drawdown']:.2%}")
        
        # Best configuration
        print(f"\n🥇 BEST CONFIGURATION:")
        print(f"Symbol: {best_config['symbol']}")
        print(f"Timeframe: {best_config['interval']}")
        print(f"Test period: {best_config['days_back']} days")
        print(f"Strategy weights: {best_config['strategy_weights']}")
        print(f"Signal threshold: {best_config['threshold']}")
        print(f"Risk level: {best_config['risk_level']:.1%}")
        print(f"\n📈 Performance:")
        print(f"Total return: {best_config['total_return']:.2%}")
        print(f"Win rate: {best_config['win_rate']:.1%}")
        print(f"Max drawdown: {best_config['max_drawdown']:.2%}")
        print(f"Sharpe ratio: {best_config['sharpe_ratio']:.2f}")
        print(f"Number of trades: {best_config['num_trades']}")
        print(f"Profit factor: {best_config['profit_factor']:.2f}")
        
        # Top 10 configurations
        print(f"\n🏅 TOP 10 CONFIGURATIONS:")
        print(f"{'Rank':<4} {'Symbol':<8} {'Timeframe':<9} {'Return':<8} {'Win Rate':<8} {'Trades':<6} {'Score':<8}")
        print("-" * 70)
        
        for i, config in enumerate(top_10, 1):
            print(f"{i:<4} {config['symbol']:<8} {config['interval']:<9} "
                  f"{config['total_return']:7.1%} {config['win_rate']:7.1%} "
                  f"{config['num_trades']:<6} {config['score']:7.1f}")
        
        # Profitable configurations summary
        if profitable:
            print(f"\n💰 PROFITABLE CONFIGURATIONS SUMMARY:")
            profitable_symbols = {}
            profitable_timeframes = {}
            
            for config in profitable:
                symbol = config['symbol']
                timeframe = config['interval']
                
                if symbol not in profitable_symbols:
                    profitable_symbols[symbol] = 0
                profitable_symbols[symbol] += 1
                
                if timeframe not in profitable_timeframes:
                    profitable_timeframes[timeframe] = 0
                profitable_timeframes[timeframe] += 1
            
            print("Best performing symbols:")
            for symbol, count in sorted(profitable_symbols.items(), key=lambda x: x[1], reverse=True):
                print(f"  {symbol}: {count} profitable configs")
            
            print("Best performing timeframes:")
            for timeframe, count in sorted(profitable_timeframes.items(), key=lambda x: x[1], reverse=True):
                print(f"  {timeframe}: {count} profitable configs")
        
        return best_config

def main():
    """
    Run comprehensive optimization
    """
    optimizer = ComprehensiveOptimizer()
    
    # Run optimization (limit to 100 tests for speed)
    results = optimizer.run_comprehensive_optimization(max_tests=100)
    
    # Display results
    best_config = optimizer.display_results(results)
    
    if best_config and best_config.get('total_return', 0) > 0:
        print(f"\n🎯 RECOMMENDATION:")
        print(f"✅ Found profitable configuration! Use these settings for live trading:")
        print(f"   Symbol: {best_config['symbol']}")
        print(f"   Timeframe: {best_config['interval']}")
        print(f"   Strategy: {best_config['strategy_weights']}")
        print(f"   Threshold: {best_config['threshold']}")
        print(f"   Risk level: {best_config['risk_level']:.1%}")
    else:
        print(f"\n⚠️ No profitable configurations found in this test.")
        print(f"Consider:")
        print(f"   - Testing longer time periods")
        print(f"   - Different market conditions")
        print(f"   - Additional strategy modifications")
    
    return results

if __name__ == "__main__":
    results = main()
