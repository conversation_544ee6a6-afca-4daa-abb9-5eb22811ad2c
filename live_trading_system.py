#!/usr/bin/env python3
"""
Live Trading System for Crypto
Real-time trading with the enhanced strategies
"""

import os
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from binance.client import Client
from dotenv import load_dotenv
import logging
from typing import Dict, List, Optional

# Import our enhanced components
from ultimate_trading_system import UltimateTradingSystem
from risk_management import AdvancedRiskManager, PortfolioManager

# Load environment variables
load_dotenv()

class LiveTradingBot:
    """
    Live trading bot with enhanced strategies
    """
    
    def __init__(self, 
                 initial_capital: float = 1000,  # Start with smaller amount for testing
                 risk_per_trade: float = 0.01,   # 1% risk per trade for live trading
                 max_positions: int = 2,         # Max 2 positions for live trading
                 config_name: str = 'balanced',
                 dry_run: bool = True):          # Start with paper trading
        
        # Initialize Binance client
        self.api_key = os.getenv("BINANCE_API_KEY")
        self.secret_key = os.getenv("BINANCE_SECRET_KEY")
        
        if not self.api_key or not self.secret_key:
            raise ValueError("Please set BINANCE_API_KEY and BINANCE_SECRET_KEY in your .env file")
        
        self.client = Client(self.api_key, self.secret_key)
        
        # Trading parameters
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_positions = max_positions
        self.config_name = config_name
        self.dry_run = dry_run
        
        # Initialize trading system
        self.trading_system = UltimateTradingSystem(
            initial_capital=initial_capital,
            risk_per_trade=risk_per_trade,
            max_positions=max_positions
        )
        
        # Portfolio tracking
        self.portfolio = {}
        self.trade_history = []
        self.current_capital = initial_capital
        
        # Setup logging
        self.setup_logging()
        
        # Trading state
        self.is_running = False
        self.last_signal_check = None
        
    def setup_logging(self):
        """Setup logging for the trading bot"""
        log_filename = f"live_trading_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_account_balance(self, asset: str = 'USDT') -> float:
        """Get current account balance"""
        try:
            if self.dry_run:
                return self.current_capital
            
            account = self.client.get_account()
            for balance in account['balances']:
                if balance['asset'] == asset:
                    return float(balance['free'])
            return 0.0
        except Exception as e:
            self.logger.error(f"Error getting balance: {e}")
            return 0.0
    
    def get_current_price(self, symbol: str) -> float:
        """Get current market price"""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            return 0.0
    
    def check_signals(self, symbol: str = 'BTCUSDT', interval: str = '15m') -> Dict:
        """Check for trading signals"""
        try:
            # Get recent data for signal generation
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=24)  # Last 24 hours
            
            # Generate signals
            signals_df = self.trading_system.generate_ensemble_signals(
                symbol, interval, start_time, end_time, self.config_name
            )
            
            if len(signals_df) == 0:
                return {'signal': 'WAIT', 'strength': 0, 'price': 0}
            
            # Get latest signal
            latest = signals_df.iloc[-1]
            current_price = self.get_current_price(symbol)
            
            return {
                'signal': latest['signal'],
                'strength': latest.get('signal_strength', 0.5),
                'price': current_price,
                'regime': latest.get('regime', 'ranging'),
                'ensemble_score': latest.get('ensemble_score', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error checking signals: {e}")
            return {'signal': 'WAIT', 'strength': 0, 'price': 0}
    
    def calculate_position_size(self, signal_strength: float, current_price: float) -> float:
        """Calculate position size for the trade"""
        available_capital = self.get_account_balance()
        risk_amount = available_capital * self.risk_per_trade
        
        # Adjust for signal strength
        adjusted_risk = risk_amount * signal_strength
        
        # Calculate position size in base currency
        position_size = adjusted_risk / current_price
        
        # Minimum position size (equivalent to $10)
        min_position = 10 / current_price
        
        return max(position_size, min_position)
    
    def place_order(self, symbol: str, side: str, quantity: float, order_type: str = 'MARKET') -> Dict:
        """Place an order (real or simulated)"""
        try:
            if self.dry_run:
                # Simulate order execution
                current_price = self.get_current_price(symbol)
                return {
                    'symbol': symbol,
                    'orderId': f"SIM_{int(time.time())}",
                    'side': side,
                    'quantity': quantity,
                    'price': current_price,
                    'status': 'FILLED',
                    'type': 'SIMULATED'
                }
            else:
                # Real order execution
                if side == 'BUY':
                    order = self.client.order_market_buy(
                        symbol=symbol,
                        quoteOrderQty=quantity * self.get_current_price(symbol)
                    )
                else:
                    order = self.client.order_market_sell(
                        symbol=symbol,
                        quantity=quantity
                    )
                return order
                
        except Exception as e:
            self.logger.error(f"Error placing {side} order: {e}")
            return None
    
    def execute_trade(self, signal_data: Dict, symbol: str) -> bool:
        """Execute a trade based on signal"""
        signal = signal_data['signal']
        strength = signal_data['strength']
        price = signal_data['price']
        
        if signal == 'WAIT':
            return False
        
        # Check if we can open new position
        if len(self.portfolio) >= self.max_positions:
            self.logger.info("Maximum positions reached, skipping trade")
            return False
        
        # Calculate position size
        position_size = self.calculate_position_size(strength, price)
        
        # Place order
        order = self.place_order(symbol, signal, position_size)
        
        if order:
            # Record the trade
            trade_record = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'side': signal,
                'quantity': position_size,
                'price': price,
                'signal_strength': strength,
                'order_id': order.get('orderId'),
                'regime': signal_data.get('regime', 'unknown')
            }
            
            self.trade_history.append(trade_record)
            
            # Update portfolio
            if signal == 'BUY':
                self.portfolio[symbol] = {
                    'quantity': position_size,
                    'entry_price': price,
                    'entry_time': datetime.now(),
                    'side': 'LONG'
                }
            
            # Update capital (for dry run)
            if self.dry_run:
                cost = position_size * price
                self.current_capital -= cost
            
            self.logger.info(f"✅ {signal} order executed: {position_size:.6f} {symbol} at ${price:.2f}")
            self.logger.info(f"Signal strength: {strength:.2f}, Regime: {signal_data.get('regime', 'unknown')}")
            
            return True
        
        return False
    
    def check_exits(self, symbol: str) -> bool:
        """Check if we should exit any positions"""
        if symbol not in self.portfolio:
            return False
        
        position = self.portfolio[symbol]
        current_price = self.get_current_price(symbol)
        entry_price = position['entry_price']
        
        # Simple exit logic (you can enhance this)
        # Exit if 5% profit or 2% loss
        if position['side'] == 'LONG':
            profit_pct = (current_price - entry_price) / entry_price
            
            if profit_pct >= 0.05:  # 5% profit
                self.close_position(symbol, 'TAKE_PROFIT')
                return True
            elif profit_pct <= -0.02:  # 2% loss
                self.close_position(symbol, 'STOP_LOSS')
                return True
        
        return False
    
    def close_position(self, symbol: str, reason: str):
        """Close a position"""
        if symbol not in self.portfolio:
            return
        
        position = self.portfolio[symbol]
        current_price = self.get_current_price(symbol)
        
        # Place sell order
        order = self.place_order(symbol, 'SELL', position['quantity'])
        
        if order:
            # Calculate P&L
            if position['side'] == 'LONG':
                pnl = (current_price - position['entry_price']) * position['quantity']
            
            # Update capital (for dry run)
            if self.dry_run:
                proceeds = position['quantity'] * current_price
                self.current_capital += proceeds
            
            # Record exit
            exit_record = {
                'timestamp': datetime.now(),
                'symbol': symbol,
                'side': 'SELL',
                'quantity': position['quantity'],
                'price': current_price,
                'pnl': pnl,
                'reason': reason,
                'hold_time': datetime.now() - position['entry_time']
            }
            
            self.trade_history.append(exit_record)
            
            # Remove from portfolio
            del self.portfolio[symbol]
            
            self.logger.info(f"🔄 Position closed: {symbol} at ${current_price:.2f}")
            self.logger.info(f"P&L: ${pnl:.2f}, Reason: {reason}")
    
    def get_performance_summary(self) -> Dict:
        """Get trading performance summary"""
        if not self.trade_history:
            return {'total_trades': 0, 'total_pnl': 0, 'win_rate': 0}
        
        # Calculate metrics
        trades_with_pnl = [t for t in self.trade_history if 'pnl' in t]
        
        if not trades_with_pnl:
            return {'total_trades': len(self.trade_history), 'total_pnl': 0, 'win_rate': 0}
        
        total_pnl = sum(t['pnl'] for t in trades_with_pnl)
        winning_trades = [t for t in trades_with_pnl if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(trades_with_pnl) if trades_with_pnl else 0
        
        current_balance = self.get_account_balance()
        total_return = (current_balance - self.initial_capital) / self.initial_capital
        
        return {
            'total_trades': len(trades_with_pnl),
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'current_balance': current_balance,
            'total_return': total_return,
            'active_positions': len(self.portfolio)
        }
    
    def run_trading_loop(self, symbol: str = 'BTCUSDT', check_interval: int = 300):
        """Main trading loop"""
        self.logger.info(f"🚀 Starting live trading bot...")
        self.logger.info(f"Mode: {'DRY RUN' if self.dry_run else 'LIVE TRADING'}")
        self.logger.info(f"Symbol: {symbol}, Check interval: {check_interval}s")
        self.logger.info(f"Initial capital: ${self.initial_capital}")
        
        self.is_running = True
        
        try:
            while self.is_running:
                # Check for exit signals first
                self.check_exits(symbol)
                
                # Check for new entry signals
                signal_data = self.check_signals(symbol)
                
                if signal_data['signal'] != 'WAIT':
                    self.logger.info(f"📊 Signal detected: {signal_data['signal']} "
                                   f"(strength: {signal_data['strength']:.2f}, "
                                   f"regime: {signal_data.get('regime', 'unknown')})")
                    
                    self.execute_trade(signal_data, symbol)
                
                # Print status every hour
                if not self.last_signal_check or \
                   (datetime.now() - self.last_signal_check).seconds > 3600:
                    
                    performance = self.get_performance_summary()
                    self.logger.info(f"📈 Status: Balance: ${performance['current_balance']:.2f}, "
                                   f"Return: {performance['total_return']:.2%}, "
                                   f"Trades: {performance['total_trades']}, "
                                   f"Win Rate: {performance['win_rate']:.1%}")
                    
                    self.last_signal_check = datetime.now()
                
                # Wait before next check
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Trading bot stopped by user")
            self.stop_trading()
        except Exception as e:
            self.logger.error(f"❌ Error in trading loop: {e}")
            self.stop_trading()
    
    def stop_trading(self):
        """Stop the trading bot"""
        self.is_running = False
        
        # Close all positions
        for symbol in list(self.portfolio.keys()):
            self.close_position(symbol, 'BOT_SHUTDOWN')
        
        # Final performance report
        performance = self.get_performance_summary()
        self.logger.info("🏁 Final Performance Report:")
        self.logger.info(f"Total Trades: {performance['total_trades']}")
        self.logger.info(f"Total P&L: ${performance['total_pnl']:.2f}")
        self.logger.info(f"Win Rate: {performance['win_rate']:.1%}")
        self.logger.info(f"Final Balance: ${performance['current_balance']:.2f}")
        self.logger.info(f"Total Return: {performance['total_return']:.2%}")

def main():
    """Main function to start live trading"""
    print("🚀 LIVE CRYPTO TRADING BOT")
    print("=" * 50)
    
    # Configuration
    bot = LiveTradingBot(
        initial_capital=1000,      # Start with $1000
        risk_per_trade=0.01,       # 1% risk per trade
        max_positions=2,           # Max 2 positions
        config_name='balanced',    # Use balanced configuration
        dry_run=True              # Start with paper trading
    )
    
    # Start trading
    try:
        bot.run_trading_loop(
            symbol='BTCUSDT',
            check_interval=300     # Check every 5 minutes
        )
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
