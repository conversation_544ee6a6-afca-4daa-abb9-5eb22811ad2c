#!/usr/bin/env python3
"""
Configuration Tester
Easily test different trading configurations for 5%+ returns
"""

import os
import sys
from datetime import datetime, timedelta, timezone
from enhanced_strategy_system import EnhancedStrategySystem

class ConfigTester:
    """
    Easy configuration testing for finding profitable setups
    """
    
    def __init__(self):
        self.strategy_system = EnhancedStrategySystem()
        
        # Predefined promising configurations
        self.test_configs = {
            'btc_4h_trend': {
                'symbol': 'BTCUSDT',
                'interval': '4h',
                'days': 90,
                'description': 'Bitcoin 4h trend following - good for trending markets'
            },
            'eth_1h_balanced': {
                'symbol': 'ETHUSDT',
                'interval': '1h',
                'days': 60,
                'description': 'Ethereum 1h balanced - current optimized config'
            },
            'sol_2h_momentum': {
                'symbol': 'SOLUSDT',
                'interval': '2h',
                'days': 75,
                'description': 'Solana 2h momentum - high volatility potential'
            },
            'bnb_6h_swing': {
                'symbol': 'BNBUSDT',
                'interval': '6h',
                'days': 120,
                'description': 'BNB 6h swing trading - stable moves'
            },
            'xrp_1h_scalp': {
                'symbol': 'XRPUSDT',
                'interval': '1h',
                'days': 45,
                'description': 'XRP 1h scalping - high frequency'
            },
            'eth_4h_conservative': {
                'symbol': 'ETHUSDT',
                'interval': '4h',
                'days': 120,
                'description': 'Ethereum 4h conservative - lower risk'
            },
            'btc_1d_position': {
                'symbol': 'BTCUSDT',
                'interval': '1d',
                'days': 180,
                'description': 'Bitcoin daily position trading - long term'
            }
        }
    
    def test_single_config(self, config_name: str) -> dict:
        """
        Test a single configuration
        """
        if config_name not in self.test_configs:
            print(f"❌ Configuration '{config_name}' not found")
            return {}
        
        config = self.test_configs[config_name]
        
        print(f"🧪 Testing: {config['description']}")
        print(f"📊 {config['symbol']} {config['interval']} ({config['days']} days)")
        
        try:
            # Test the configuration
            result = self.strategy_system.test_enhanced_system(
                symbol=config['symbol'],
                interval=config['interval'],
                days_back=config['days']
            )
            
            if 'error' in result:
                print(f"❌ Error: {result['error']}")
                return {}
            
            # Display results
            total_return = result.get('total_return', 0)
            win_rate = result.get('win_rate', 0)
            max_drawdown = result.get('max_drawdown', 0)
            num_trades = result.get('num_trades', 0)
            sharpe_ratio = result.get('sharpe_ratio', 0)
            
            print(f"\n📈 Results:")
            print(f"  Total Return: {total_return:.2%}")
            print(f"  Win Rate: {win_rate:.1%}")
            print(f"  Max Drawdown: {max_drawdown:.2%}")
            print(f"  Number of Trades: {num_trades}")
            print(f"  Sharpe Ratio: {sharpe_ratio:.2f}")
            
            # Evaluation
            meets_target = total_return >= 0.05
            is_profitable = total_return > 0
            good_win_rate = win_rate >= 0.6
            controlled_risk = max_drawdown <= 0.1
            sufficient_trades = num_trades >= 5
            
            print(f"\n🎯 Evaluation:")
            print(f"  Meets 5% Target: {'✅' if meets_target else '❌'}")
            print(f"  Profitable: {'✅' if is_profitable else '❌'}")
            print(f"  Good Win Rate: {'✅' if good_win_rate else '❌'}")
            print(f"  Controlled Risk: {'✅' if controlled_risk else '❌'}")
            print(f"  Sufficient Trades: {'✅' if sufficient_trades else '❌'}")
            
            # Overall recommendation
            if meets_target and good_win_rate and controlled_risk:
                recommendation = "🚀 EXCELLENT - Deploy immediately!"
            elif is_profitable and win_rate >= 0.5 and max_drawdown <= 0.15:
                recommendation = "✅ GOOD - Consider for live testing"
            elif is_profitable:
                recommendation = "⚠️ MARGINAL - Needs optimization"
            else:
                recommendation = "❌ POOR - Try different configuration"
            
            print(f"\n🎯 Recommendation: {recommendation}")
            
            return {
                'config_name': config_name,
                'config': config,
                'results': result,
                'meets_target': meets_target,
                'recommendation': recommendation,
                'score': total_return * 100 + win_rate * 50 - max_drawdown * 100
            }
            
        except Exception as e:
            print(f"❌ Test failed: {e}")
            return {}
    
    def test_all_configs(self) -> dict:
        """
        Test all predefined configurations
        """
        print("🚀 CONFIGURATION TESTER - TESTING ALL CONFIGS")
        print("=" * 60)
        
        all_results = []
        
        for config_name in self.test_configs.keys():
            print(f"\n{'='*20} {config_name.upper()} {'='*20}")
            
            result = self.test_single_config(config_name)
            
            if result:
                all_results.append(result)
        
        # Summary
        print(f"\n" + "="*60)
        print("🏆 SUMMARY OF ALL CONFIGURATIONS")
        print("="*60)
        
        if not all_results:
            print("❌ No successful tests")
            return {}
        
        # Sort by score
        all_results.sort(key=lambda x: x['score'], reverse=True)
        
        print(f"\n📊 RANKING:")
        print(f"{'Rank':<4} {'Config':<20} {'Return':<8} {'Win Rate':<9} {'Trades':<7} {'Status':<12}")
        print("-" * 70)
        
        for i, result in enumerate(all_results, 1):
            config_name = result['config_name']
            results = result['results']
            status = "✅ TARGET" if result['meets_target'] else "⚠️ PARTIAL" if results.get('total_return', 0) > 0 else "❌ LOSS"
            
            print(f"{i:<4} {config_name:<20} {results.get('total_return', 0):7.1%} "
                  f"{results.get('win_rate', 0):8.1%} {results.get('num_trades', 0):6d} {status:<12}")
        
        # Best configurations
        profitable_configs = [r for r in all_results if r['meets_target']]
        
        if profitable_configs:
            print(f"\n🎯 CONFIGURATIONS MEETING 5% TARGET: {len(profitable_configs)}")
            
            for result in profitable_configs:
                config = result['config']
                results = result['results']
                
                print(f"\n✅ {result['config_name'].upper()}:")
                print(f"   Symbol: {config['symbol']}")
                print(f"   Timeframe: {config['interval']}")
                print(f"   Return: {results.get('total_return', 0):.2%}")
                print(f"   Win Rate: {results.get('win_rate', 0):.1%}")
                print(f"   Trades: {results.get('num_trades', 0)}")
                print(f"   Recommendation: {result['recommendation']}")
        
        else:
            print(f"\n❌ NO CONFIGURATIONS MEET 5% TARGET")
            
            # Show best performing ones
            positive_configs = [r for r in all_results if r['results'].get('total_return', 0) > 0]
            
            if positive_configs:
                print(f"\n⚠️ BEST PERFORMING CONFIGURATIONS:")
                
                for result in positive_configs[:3]:  # Top 3
                    config = result['config']
                    results = result['results']
                    
                    print(f"\n⚠️ {result['config_name'].upper()}:")
                    print(f"   Return: {results.get('total_return', 0):.2%}")
                    print(f"   Win Rate: {results.get('win_rate', 0):.1%}")
                    print(f"   Recommendation: {result['recommendation']}")
        
        return {
            'all_results': all_results,
            'profitable_configs': profitable_configs,
            'best_config': all_results[0] if all_results else None,
            'mission_status': len(profitable_configs) > 0
        }
    
    def generate_live_config(self, config_name: str):
        """
        Generate live trading configuration code
        """
        if config_name not in self.test_configs:
            print(f"❌ Configuration '{config_name}' not found")
            return
        
        config = self.test_configs[config_name]
        
        print(f"\n🔧 LIVE TRADING CONFIGURATION FOR: {config_name.upper()}")
        print("="*60)
        print(f"Description: {config['description']}")
        print(f"\n📝 Edit live_trading_with_telegram.py lines 570-575:")
        print(f"""
bot = MockTradingBot(
    initial_capital=1000,
    risk_per_trade=0.01,
    max_positions=2,
    symbol='{config['symbol']}',
    interval='{config['interval']}'
)
        """)
        
        print(f"💡 Recommended check interval:")
        if config['interval'] in ['15m', '30m', '1h']:
            interval_seconds = 900  # 15 minutes
        elif config['interval'] in ['2h', '4h']:
            interval_seconds = 1800  # 30 minutes
        else:
            interval_seconds = 3600  # 1 hour
        
        print(f"   check_interval={interval_seconds}  # Check every {interval_seconds//60} minutes")
        
        print(f"\n🚀 To start live trading with this configuration:")
        print(f"   1. Edit the configuration in live_trading_with_telegram.py")
        print(f"   2. Run: python live_trading_with_telegram.py")
        print(f"   3. Monitor Telegram notifications")

def main():
    """
    Main function with interactive menu
    """
    tester = ConfigTester()
    
    print("🚀 TRADING CONFIGURATION TESTER")
    print("=" * 50)
    print("🎯 Mission: Find configurations with 5%+ returns")
    
    while True:
        print(f"\n📋 MENU:")
        print(f"1. Test all configurations")
        print(f"2. Test specific configuration")
        print(f"3. List available configurations")
        print(f"4. Generate live config code")
        print(f"5. Exit")
        
        choice = input(f"\nEnter your choice (1-5): ").strip()
        
        if choice == '1':
            print(f"\n🔄 Testing all configurations...")
            results = tester.test_all_configs()
            
        elif choice == '2':
            print(f"\nAvailable configurations:")
            for i, (name, config) in enumerate(tester.test_configs.items(), 1):
                print(f"{i}. {name} - {config['description']}")
            
            config_choice = input(f"\nEnter configuration name: ").strip()
            if config_choice in tester.test_configs:
                tester.test_single_config(config_choice)
            else:
                print(f"❌ Configuration not found")
        
        elif choice == '3':
            print(f"\n📋 Available Configurations:")
            for name, config in tester.test_configs.items():
                print(f"  {name}: {config['description']}")
        
        elif choice == '4':
            config_name = input(f"\nEnter configuration name for live trading: ").strip()
            tester.generate_live_config(config_name)
        
        elif choice == '5':
            print(f"👋 Goodbye!")
            break
        
        else:
            print(f"❌ Invalid choice")

if __name__ == "__main__":
    main()
