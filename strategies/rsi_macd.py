import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_rsi_macd(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=90):
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=lookback_days)

    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)
        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    df = pd.concat(df_list, ignore_index=True)

    df = generate_rsi_macd_signals(df)
    return df

def generate_rsi_macd_signals(df):
    df["rsi"] = compute_rsi(df["close"], 14)
    df["macd"], df["signal_line"] = compute_macd(df["close"])
    df["signal"] = "WAIT"
    df.loc[(df["rsi"] < 30) & (df["macd"] > df["signal_line"]), "signal"] = "BUY"
    df.loc[(df["rsi"] > 70) & (df["macd"] < df["signal_line"]), "signal"] = "SELL"
    return df

def compute_rsi(series, period=14):
    delta = series.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()

    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def compute_macd(series, short_period=12, long_period=26, signal_period=9):
    ema_short = series.ewm(span=short_period, adjust=False).mean()
    ema_long = series.ewm(span=long_period, adjust=False).mean()
    macd = ema_short - ema_long
    signal = macd.ewm(span=signal_period, adjust=False).mean()
    return macd, signal
