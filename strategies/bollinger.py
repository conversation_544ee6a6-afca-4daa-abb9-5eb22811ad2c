import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_ohlcv(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=90):
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=lookback_days)

    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)
        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def generate_bollinger_signals(df, window=20, num_std=2):
    df["rolling_mean"] = df["close"].rolling(window).mean()
    df["rolling_std"] = df["close"].rolling(window).std()
    df["upper_band"] = df["rolling_mean"] + (num_std * df["rolling_std"])
    df["lower_band"] = df["rolling_mean"] - (num_std * df["rolling_std"])
    df["signal"] = "WAIT"
    df.loc[df["close"] < df["lower_band"], "signal"] = "BUY"
    df.loc[df["close"] > df["upper_band"], "signal"] = "SELL"
    return df

# shortcut
def fetch_bollinger(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=90):
    df = fetch_ohlcv(symbol, interval, lookback_days)
    return generate_bollinger_signals(df)

# Kullanım örneği
if __name__ == "__main__":
    df = fetch_ohlcv()
    df = generate_bollinger_signals(df)
    print(df[["timestamp", "close", "middle_band", "upper_band", "lower_band", "signal"]].tail(20))
