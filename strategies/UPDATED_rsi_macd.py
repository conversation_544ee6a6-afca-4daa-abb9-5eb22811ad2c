import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_rsi_macd(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=None, start_time=None, end_time=None):
    if start_time and end_time:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    elif lookback_days:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=lookback_days)
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    else:
        raise ValueError("lookback_days veya start_time ve end_time verilmelidir.")

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        # cast all OHLC + volume
        for col in ["open","high","low","close","volume"]:
            df[col] = df[col].astype(float)

        # keep timestamp + full OHLC/volume so regime detector works
        df_list.append(df[["timestamp","open","high","low","close","volume"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    df = pd.concat(df_list, ignore_index=True)
    df = generate_rsi_macd_signals(df)
    return df


def generate_rsi_macd_signals(df):
    """Enhanced RSI+MACD strategy with proper signal confirmation"""
    df = df.copy()

    # Calculate indicators
    df["rsi"] = compute_rsi(df["close"], 14)
    df["macd"], df["signal_line"] = compute_macd(df["close"])
    df["macd_histogram"] = df["macd"] - df["signal_line"]

    # Calculate trend filter (20-period EMA)
    df["trend_ema"] = df["close"].ewm(span=20, adjust=False).mean()
    df["trend_direction"] = np.where(df["close"] > df["trend_ema"], 1, -1)

    # Calculate volatility filter
    df["atr"] = df["close"].rolling(14).apply(lambda x: np.mean(np.abs(x.diff())))
    df["volatility_percentile"] = df["atr"].rolling(50).rank(pct=True)

    # Previous values for crossover detection
    df["prev_macd"] = df["macd"].shift(1)
    df["prev_signal_line"] = df["signal_line"].shift(1)
    df["prev_rsi"] = df["rsi"].shift(1)

    # Initialize signals
    df["signal"] = "WAIT"

    # Enhanced BUY conditions
    buy_conditions = (
        # RSI oversold but recovering
        (df["rsi"] < 35) & (df["prev_rsi"] < df["rsi"]) &
        # MACD bullish crossover
        (df["macd"] > df["signal_line"]) & (df["prev_macd"] <= df["prev_signal_line"]) &
        # MACD histogram increasing
        (df["macd_histogram"] > df["macd_histogram"].shift(1)) &
        # Trend alignment (only buy in uptrends or neutral)
        (df["trend_direction"] >= 0) &
        # Avoid high volatility periods
        (df["volatility_percentile"] < 0.8)
    )

    # Enhanced SELL conditions
    sell_conditions = (
        # RSI overbought and declining
        (df["rsi"] > 65) & (df["prev_rsi"] > df["rsi"]) &
        # MACD bearish crossover
        (df["macd"] < df["signal_line"]) & (df["prev_macd"] >= df["prev_signal_line"]) &
        # MACD histogram decreasing
        (df["macd_histogram"] < df["macd_histogram"].shift(1)) &
        # Trend alignment (only sell in downtrends or neutral)
        (df["trend_direction"] <= 0) &
        # Avoid high volatility periods
        (df["volatility_percentile"] < 0.8)
    )

    df.loc[buy_conditions, "signal"] = "BUY"
    df.loc[sell_conditions, "signal"] = "SELL"

    # Add signal strength scoring
    df["signal_strength"] = 0.0

    # RSI component (0-0.4)
    rsi_strength = np.where(df["rsi"] < 30, (30 - df["rsi"]) / 30,
                           np.where(df["rsi"] > 70, (df["rsi"] - 70) / 30, 0)) * 0.4

    # MACD component (0-0.4)
    macd_strength = np.abs(df["macd_histogram"]) / df["atr"] * 0.4
    macd_strength = np.clip(macd_strength, 0, 0.4)

    # Trend alignment component (0-0.2)
    trend_strength = np.where(
        (df["signal"] == "BUY") & (df["trend_direction"] > 0), 0.2,
        np.where((df["signal"] == "SELL") & (df["trend_direction"] < 0), 0.2, 0)
    )

    df["signal_strength"] = rsi_strength + macd_strength + trend_strength
    df["score_rsi_macd"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})

    return df

def compute_rsi(series, period=14):
    delta = series.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()

    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def compute_macd(series, short_period=12, long_period=26, signal_period=9):
    ema_short = series.ewm(span=short_period, adjust=False).mean()
    ema_long = series.ewm(span=long_period, adjust=False).mean()
    macd = ema_short - ema_long
    signal = macd.ewm(span=signal_period, adjust=False).mean()
    return macd, signal
