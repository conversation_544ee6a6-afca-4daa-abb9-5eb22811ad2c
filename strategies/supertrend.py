# supertrend.py
import pandas as pd
import numpy as np
from binance.client import Client
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta

# ENV
load_dotenv()
client = Client(os.getenv("BINANCE_API_KEY"), os.getenv("BINANCE_SECRET_KEY"))

def fetch_ohlcv(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=None, end_time=None):
    if end_time is None:
        end_time = datetime.utcnow()
    if start_time is None:
        start_time = end_time - timedelta(days=90)

    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(symbol=symbol, interval=interval, startTime=current_start, endTime=end_ts)
        if not klines:
            break
        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)
        df["high"] = df["high"].astype(float)
        df["low"] = df["low"].astype(float)
        df_list.append(df[["timestamp", "high", "low", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def compute_supertrend(df, atr_period=7, multiplier=1.5):
    df = df.copy()
    hl2 = (df["high"] + df["low"]) / 2
    df["tr"] = np.maximum(df["high"] - df["low"],
                          np.maximum(abs(df["high"] - df["close"].shift(1)),
                                     abs(df["low"] - df["close"].shift(1))))
    df["atr"] = df["tr"].rolling(window=atr_period).mean()
    df["upper_band"] = hl2 + (multiplier * df["atr"])
    df["lower_band"] = hl2 - (multiplier * df["atr"])

    df["supertrend"] = True
    for i in range(1, len(df)):
        prev_close = df["close"].iloc[i - 1]
        curr_close = df["close"].iloc[i]
        if df["supertrend"].iloc[i - 1] and curr_close < df["lower_band"].iloc[i]:
            df.at[i, "supertrend"] = False
        elif not df["supertrend"].iloc[i - 1] and curr_close > df["upper_band"].iloc[i]:
            df.at[i, "supertrend"] = True
        else:
            df.at[i, "supertrend"] = df["supertrend"].iloc[i - 1]
            if df["supertrend"].iloc[i]:
                df.at[i, "lower_band"] = max(df["lower_band"].iloc[i], df["lower_band"].iloc[i - 1])
            else:
                df.at[i, "upper_band"] = min(df["upper_band"].iloc[i], df["upper_band"].iloc[i - 1])

    return df

def generate_supertrend_signals(df):
    df = compute_supertrend(df)
    df["signal"] = "WAIT"
    df.loc[(df["supertrend"]) & (df["supertrend"].shift(1) == False), "signal"] = "BUY"
    df.loc[(~df["supertrend"]) & (df["supertrend"].shift(1) == True), "signal"] = "SELL"
    df["score_supertrend"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})
    return df

def fetch_supertrend(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=None, end_time=None):
    df = fetch_ohlcv(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time)
    return generate_supertrend_signals(df)

if __name__ == "__main__":
    df = fetch_supertrend()
    print(df[["timestamp", "close", "supertrend", "signal"]].tail(20))
