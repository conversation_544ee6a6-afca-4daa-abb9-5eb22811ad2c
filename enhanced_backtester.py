#!/usr/bin/env python3
"""
Enhanced Backtesting Framework with Realistic Transaction Costs
Implements proper slippage, fees, and execution modeling for crypto trading
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
from risk_management import AdvancedRiskManager, PortfolioManager

class EnhancedBacktester:
    """
    Enhanced backtesting with realistic transaction costs and execution modeling
    """
    
    def __init__(self, initial_capital: float = 10000, 
                 maker_fee: float = 0.001, taker_fee: float = 0.001,
                 slippage_factor: float = 0.0005, min_trade_size: float = 10):
        
        self.initial_capital = initial_capital
        self.maker_fee = maker_fee
        self.taker_fee = taker_fee
        self.slippage_factor = slippage_factor
        self.min_trade_size = min_trade_size
        
        # Initialize risk and portfolio managers
        self.risk_manager = AdvancedRiskManager(initial_capital)
        self.portfolio_manager = PortfolioManager()
        
        # Tracking variables
        self.reset_state()
    
    def reset_state(self):
        """Reset backtester state for new run"""
        self.current_capital = self.initial_capital
        self.equity_curve = []
        self.trades = []
        self.daily_returns = []
        self.positions = {}
        self.trade_count = 0
        
    def calculate_slippage(self, price: float, volume: float, is_aggressive: bool = True) -> float:
        """
        Calculate realistic slippage based on order size and market impact
        """
        # Base slippage
        base_slippage = self.slippage_factor
        
        # Volume impact (simplified model)
        volume_impact = min(volume / 1000000, 0.002)  # Cap at 0.2%
        
        # Market timing impact (aggressive vs passive)
        timing_impact = base_slippage if is_aggressive else base_slippage * 0.5
        
        total_slippage = timing_impact + volume_impact
        return min(total_slippage, 0.01)  # Cap at 1%
    
    def calculate_fees(self, trade_value: float, is_maker: bool = False) -> float:
        """
        Calculate trading fees with maker/taker distinction
        """
        fee_rate = self.maker_fee if is_maker else self.taker_fee
        return trade_value * fee_rate
    
    def execute_trade(self, symbol: str, signal: str, price: float, 
                     signal_strength: float, df: pd.DataFrame, timestamp: datetime) -> bool:
        """
        Execute a trade with realistic costs and risk management
        """
        # Check if we can open this position
        if not self.portfolio_manager.can_open_position(symbol, signal):
            return False
        
        # Calculate position size
        volatility_rank = df.get('volatility_rank', pd.Series([0.5])).iloc[-1] if hasattr(df.get('volatility_rank', 0.5), 'iloc') else df.get('volatility_rank', 0.5)

        # Create a simple DataFrame for risk calculation
        risk_df = pd.DataFrame({
            'atr': [df.get('atr', pd.Series([price * 0.02])).iloc[-1] if hasattr(df.get('atr', price * 0.02), 'iloc') else df.get('atr', price * 0.02)],
            'volatility_rank': [volatility_rank]
        })

        position_size = self.risk_manager.calculate_position_size(
            risk_df, self.current_capital, signal_strength, volatility_rank
        )
        
        # Check minimum trade size
        if position_size < self.min_trade_size:
            return False
        
        # Calculate slippage and fees
        slippage = self.calculate_slippage(price, position_size, is_aggressive=True)
        
        if signal == 'BUY':
            execution_price = price * (1 + slippage)
        else:
            execution_price = price * (1 - slippage)
        
        # Calculate quantity and costs
        quantity = position_size / execution_price
        trade_value = quantity * execution_price
        fees = self.calculate_fees(trade_value, is_maker=False)
        total_cost = trade_value + fees
        
        # Check if we have enough capital
        if total_cost > self.current_capital:
            return False
        
        # Calculate stop loss and take profit
        stops = self.risk_manager.calculate_stop_loss(risk_df, execution_price, signal, signal_strength)
        
        # Execute the trade
        self.current_capital -= total_cost
        
        # Add to portfolio
        self.portfolio_manager.add_position(
            symbol, signal, execution_price, quantity,
            stops['stop_loss'], stops['take_profit']
        )
        
        # Record trade entry
        self.trades.append({
            'symbol': symbol,
            'type': 'ENTRY',
            'signal': signal,
            'timestamp': timestamp,
            'price': execution_price,
            'quantity': quantity,
            'fees': fees,
            'slippage': slippage,
            'signal_strength': signal_strength,
            'stop_loss': stops['stop_loss'],
            'take_profit': stops['take_profit']
        })
        
        self.trade_count += 1
        return True
    
    def check_exits(self, symbol: str, current_price: float, timestamp: datetime) -> bool:
        """
        Check if any positions should be closed
        """
        if symbol not in self.portfolio_manager.active_positions:
            return False
        
        position = self.portfolio_manager.active_positions[symbol]
        exit_triggered = False
        exit_reason = ""
        exit_price = current_price
        
        # Check stop loss
        if position['signal_type'] == 'BUY' and current_price <= position['stop_loss']:
            exit_price = position['stop_loss']
            exit_reason = 'STOP_LOSS'
            exit_triggered = True
        elif position['signal_type'] == 'SELL' and current_price >= position['stop_loss']:
            exit_price = position['stop_loss']
            exit_reason = 'STOP_LOSS'
            exit_triggered = True
        
        # Check take profit
        elif position['signal_type'] == 'BUY' and current_price >= position['take_profit']:
            exit_price = position['take_profit']
            exit_reason = 'TAKE_PROFIT'
            exit_triggered = True
        elif position['signal_type'] == 'SELL' and current_price <= position['take_profit']:
            exit_price = position['take_profit']
            exit_reason = 'TAKE_PROFIT'
            exit_triggered = True
        
        if exit_triggered:
            self.close_position(symbol, exit_price, exit_reason, timestamp)
            return True
        
        return False
    
    def close_position(self, symbol: str, exit_price: float, exit_reason: str, timestamp: datetime):
        """
        Close a position and calculate P&L
        """
        if symbol not in self.portfolio_manager.active_positions:
            return
        
        position = self.portfolio_manager.active_positions[symbol]
        
        # Calculate slippage and fees for exit
        trade_value = position['position_size'] * exit_price
        slippage = self.calculate_slippage(exit_price, trade_value, is_aggressive=True)
        
        if position['signal_type'] == 'BUY':
            final_exit_price = exit_price * (1 - slippage)
        else:
            final_exit_price = exit_price * (1 + slippage)
        
        final_trade_value = position['position_size'] * final_exit_price
        fees = self.calculate_fees(final_trade_value, is_maker=False)
        
        # Calculate P&L
        if position['signal_type'] == 'BUY':
            gross_pnl = (final_exit_price - position['entry_price']) * position['position_size']
        else:
            gross_pnl = (position['entry_price'] - final_exit_price) * position['position_size']
        
        net_pnl = gross_pnl - fees
        
        # Update capital
        self.current_capital += (final_trade_value - fees)
        
        # Update risk manager
        self.risk_manager.update_risk_state(self.current_capital, net_pnl)
        
        # Remove from portfolio and record trade
        self.portfolio_manager.remove_position(symbol, final_exit_price, exit_reason)
        
        self.trades.append({
            'symbol': symbol,
            'type': 'EXIT',
            'signal': position['signal_type'],
            'timestamp': timestamp,
            'price': final_exit_price,
            'quantity': position['position_size'],
            'fees': fees,
            'slippage': slippage,
            'pnl': net_pnl,
            'exit_reason': exit_reason
        })
    
    def backtest_strategy(self, signals_df: pd.DataFrame, symbol: str = "BTCUSDT") -> Dict:
        """
        Run backtest on strategy signals
        """
        self.reset_state()
        df = signals_df.copy().reset_index(drop=True)
        
        for i, row in df.iterrows():
            timestamp = row['timestamp']
            current_price = row['close']
            signal = row.get('signal', 'WAIT')
            signal_strength = row.get('signal_strength', 0.5)
            
            # Check for exits first
            self.check_exits(symbol, current_price, timestamp)
            
            # Check for new entries
            if signal in ['BUY', 'SELL'] and not self.risk_manager.should_stop_trading():
                current_row_df = df.iloc[max(0, i-20):i+1]  # Last 20 periods for context
                self.execute_trade(symbol, signal, current_price, signal_strength, 
                                 current_row_df, timestamp)
            
            # Calculate current equity (including unrealized P&L)
            current_equity = self.current_capital
            for pos_symbol, position in self.portfolio_manager.active_positions.items():
                if position['signal_type'] == 'BUY':
                    unrealized_pnl = (current_price - position['entry_price']) * position['position_size']
                else:
                    unrealized_pnl = (position['entry_price'] - current_price) * position['position_size']
                current_equity += unrealized_pnl
            
            self.equity_curve.append(current_equity)
        
        # Close any remaining positions
        for symbol in list(self.portfolio_manager.active_positions.keys()):
            final_price = df['close'].iloc[-1]
            self.close_position(symbol, final_price, 'END_OF_BACKTEST', df['timestamp'].iloc[-1])
        
        return self.calculate_performance_metrics()
    
    def calculate_performance_metrics(self) -> Dict:
        """
        Calculate comprehensive performance metrics
        """
        if not self.equity_curve:
            return {'error': 'No equity curve data'}
        
        equity_array = np.array(self.equity_curve)
        returns = np.diff(equity_array) / equity_array[:-1]
        
        # Basic metrics
        total_return = (equity_array[-1] - self.initial_capital) / self.initial_capital
        
        # Risk metrics
        volatility = np.std(returns) * np.sqrt(252 * 24 * 4)  # Annualized for 15min data
        sharpe_ratio = (np.mean(returns) * 252 * 24 * 4) / volatility if volatility > 0 else 0
        
        # Drawdown calculation
        peak = np.maximum.accumulate(equity_array)
        drawdown = (peak - equity_array) / peak
        max_drawdown = np.max(drawdown)
        
        # Trade statistics
        trade_pnls = [trade['pnl'] for trade in self.trades if trade['type'] == 'EXIT']
        
        if trade_pnls:
            winning_trades = [pnl for pnl in trade_pnls if pnl > 0]
            losing_trades = [pnl for pnl in trade_pnls if pnl < 0]
            
            win_rate = len(winning_trades) / len(trade_pnls)
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            profit_factor = abs(sum(winning_trades) / sum(losing_trades)) if losing_trades else float('inf')
            expectancy = win_rate * avg_win + (1 - win_rate) * avg_loss
        else:
            win_rate = avg_win = avg_loss = profit_factor = expectancy = 0
        
        return {
            'total_return': total_return,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'volatility': volatility,
            'num_trades': len(trade_pnls),
            'win_rate': win_rate,
            'avg_win': avg_win,
            'avg_loss': avg_loss,
            'profit_factor': profit_factor,
            'expectancy': expectancy,
            'final_capital': equity_array[-1],
            'equity_curve': self.equity_curve,
            'trades': self.trades,
            'risk_metrics': self.risk_manager.get_risk_metrics()
        }
    
    def plot_results(self, save_path: str = None):
        """
        Plot backtest results
        """
        if not self.equity_curve:
            print("No data to plot")
            return
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # Equity curve
        ax1.plot(self.equity_curve, label='Portfolio Value', linewidth=2)
        ax1.axhline(y=self.initial_capital, color='r', linestyle='--', alpha=0.7, label='Initial Capital')
        ax1.set_title('Portfolio Equity Curve')
        ax1.set_ylabel('Portfolio Value ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Drawdown
        equity_array = np.array(self.equity_curve)
        peak = np.maximum.accumulate(equity_array)
        drawdown = (peak - equity_array) / peak * 100
        
        ax2.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        ax2.set_title('Drawdown (%)')
        ax2.set_ylabel('Drawdown (%)')
        ax2.set_xlabel('Time Period')
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()

if __name__ == "__main__":
    # Test the enhanced backtester
    print("Enhanced Backtester Test")
    
    # Create sample data
    dates = pd.date_range('2023-01-01', periods=1000, freq='15min')
    np.random.seed(42)
    
    # Generate sample price data
    returns = np.random.normal(0.0001, 0.02, 1000)
    prices = 50000 * np.exp(np.cumsum(returns))
    
    # Generate sample signals
    signals = np.random.choice(['BUY', 'SELL', 'WAIT'], 1000, p=[0.05, 0.05, 0.9])
    signal_strength = np.random.uniform(0.3, 1.0, 1000)
    
    test_df = pd.DataFrame({
        'timestamp': dates,
        'close': prices,
        'signal': signals,
        'signal_strength': signal_strength,
        'atr': prices * 0.02,
        'volatility_rank': np.random.uniform(0, 1, 1000)
    })
    
    # Run backtest
    backtester = EnhancedBacktester(initial_capital=10000)
    results = backtester.backtest_strategy(test_df)
    
    print(f"Total Return: {results['total_return']:.2%}")
    print(f"Sharpe Ratio: {results['sharpe_ratio']:.2f}")
    print(f"Max Drawdown: {results['max_drawdown']:.2%}")
    print(f"Number of Trades: {results['num_trades']}")
    print(f"Win Rate: {results['win_rate']:.1%}")
    print(f"Profit Factor: {results['profit_factor']:.2f}")
