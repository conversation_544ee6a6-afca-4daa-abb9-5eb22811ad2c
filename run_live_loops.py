import json, time, os
import pandas as pd
from datetime import datetime, timedelta, timezone
from binance.client import Client

from config.env import BINANCE_API_KEY, BINANCE_SECRET_KEY
from utils.telegram_notify import send_telegram_message

from strategies.UPDATED_bollinger    import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd      import fetch_rsi_macd
from strategies.supertrend           import fetch_supertrend
from strategies.stochastic_rsi       import fetch_stoch_rsi

# static
SIGNAL_SCORE = {"BUY": 1, "WAIT": 0, "SELL": -1}

def load_config(path="config.json"):
    """Read the JSON config freshly each loop."""
    with open(path) as f:
        return json.load(f)

def get_btc_price():
    c = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)
    return float(c.get_symbol_ticker(symbol="BTCUSDT")["price"])

def fetch_live_signals(symbol, interval, past, now):
    """Grab each strat’s signals over [past → now]."""
    return {
      "bollinger":      fetch_bollinger(symbol, interval, start_time=past, end_time=now),
      "ema_crossover":  fetch_ema_crossover(symbol, interval, start_time=past, end_time=now),
      "rsi_macd":       fetch_rsi_macd(symbol, interval, start_time=past, end_time=now),
      "stochastic_rsi": fetch_stoch_rsi(symbol, interval, start_time=past, end_time=now),
      "supertrend":     fetch_supertrend(symbol, interval, start_time=past, end_time=now),
    }

def generate_final_signal(signals, threshold, weights):
    """Merge, weight, threshold → latest row + full df."""
    df = None
    for name, strat_df in signals.items():
        s = (
            strat_df[["timestamp","signal"]]
            .rename(columns={"signal": f"signal_{name}"})
        )
        s[f"score_{name}"] = s[f"signal_{name}"].map(SIGNAL_SCORE)
        df = s if df is None else df.merge(s, on="timestamp", how="outer")

    df.sort_values("timestamp", inplace=True)
    df.ffill(inplace=True); df.bfill(inplace=True)

    # ensure every weight has a score column
    for name in weights:
        col = f"score_{name}"
        if col not in df:
            df[col] = 0

    # weighted score
    df["weighted_score"] = sum(
        df[f"score_{n}"] * w for n, w in weights.items()
    )

    # apply threshold
    df["final_signal"] = df["weighted_score"].apply(
        lambda x: "BUY"  if x >  threshold
                  else "SELL" if x < -threshold
                             else "WAIT"
    )

    return df.iloc[-1], df

def run_loop_spot():
    """Spot‐only version (LONG only)."""
    cfg = load_config()
    balance  = cfg["initial_balance_spot"]
    position = None
    entry    = 0.0
    qty       = 0.0

    while True:
        try:
            cfg = load_config()  # reload any updates
            interval       = cfg["poll_interval"]
            lookback       = cfg["lookback_days"]
            threshold      = cfg["signal_threshold"]
            weights        = cfg["strategy_weights"]
            risk_pct       = cfg["risk_per_trade"]
            sl_pct         = cfg["stop_loss_pct"]
            tp_pct         = cfg["take_profit_pct"]
            fee            = cfg["fee_rate"]
            slip           = cfg["slippage_rate"]

            now  = datetime.now(timezone.utc)
            past = now - timedelta(days=lookback)

            print(f"🔁 Polling at {now.isoformat()}")

            signals, _ = generate_final_signal(
                fetch_live_signals("BTCUSDT","15m", past, now),
                threshold, weights
            )
            sig   = signals["final_signal"]
            price = get_btc_price()
            time_str = now.strftime("%Y-%m-%d %H:%M:%S")

            # ——— entry —————————————————————
            if position is None and sig == "BUY":
                risk_usd   = balance * risk_pct
                sl_price   = price * (1 - sl_pct)
                tp_price   = price * (1 + tp_pct)
                # qty sized so max loss ≈ risk_usd
                qty        = risk_usd / (price - sl_price)
                cost       = qty * price * (1 + slip)
                fee_cost   = cost * fee
                if cost + fee_cost <= balance:
                    balance   -= (cost + fee_cost)
                    position   = "LONG"
                    entry      = price
                    send_telegram_message(
                        f"🟢 OPEN LONG {qty:.6f} BTC @ ${price:.2f}\n"
                        f"   SL @ ${sl_price:.2f}  TP @ ${tp_price:.2f}\n"
                        f"   Bal: ${balance:.2f}"
                    )

            # ——— exit ——————————————————————
            elif position == "LONG":
                sl_price = entry * (1 - sl_pct)
                tp_price = entry * (1 + tp_pct)
                if price <= sl_price or price >= tp_price or sig == "SELL":
                    proceeds = qty * price * (1 - slip)
                    fee_out  = proceeds * fee
                    pnl      = proceeds - fee_out - (qty * entry * (1+slip) * fee)
                    balance += (proceeds - fee_out)
                    send_telegram_message(
                        f"🛑 CLOSE LONG @ ${price:.2f} | P&L ${pnl:.2f} | Bal ${balance:.2f}"
                    )
                    position = None

            # ——— heartbeat ——————————————————
            detail = "\n".join(
                f"📉 {n.upper()}: {signals[f'signal_{n}']}"
                for n in weights
            )
            send_telegram_message(
                f"🕒 {time_str}\n"
                f"💰 BTC: ${price:.2f}\n"
                f"{detail}\n"
                f"📊 Final: {sig} ({signals['weighted_score']:.2f})\n"
                f"💼 Bal: ${balance:.2f} | Pos: {position or 'NONE'}"
            )

        except Exception as e:
            send_telegram_message(f"⚠️ Runtime error: {e}")
            print("ERROR", e)

        print(f"⏱️ Sleeping {interval}s…\n")
        time.sleep(interval)

if __name__ == "__main__":
    run_loop_spot()
