import os
import pandas as pd
import matplotlib.pyplot as plt
from strategies.rsi_macd import fetch_rsi_macd
from strategies.ema_crossover import fetch_ema_crossover
from strategies.bollinger import fetch_bollinger

PLOT_DIR = "plots"
os.makedirs(PLOT_DIR, exist_ok=True)

def fetch_all(symbol="BTCUSDT", interval="15m", lookback_days=90):
    df_rsi = fetch_rsi_macd(symbol=symbol, interval=interval, lookback_days=lookback_days)
    df_rsi = df_rsi.rename(columns={"signal": "signal_rsi"})

    df_ema = fetch_ema_crossover(symbol=symbol, interval=interval, lookback_days=lookback_days)
    df_ema = df_ema.rename(columns={"signal": "signal_ema"})

    df_bb = fetch_bollinger(symbol=symbol, interval=interval, lookback_days=lookback_days)
    df_bb = df_bb.rename(columns={"signal": "signal_bb"})

    df = df_rsi.merge(df_ema[["timestamp", "signal_ema"]], on="timestamp", how="inner")
    df = df.merge(df_bb[["timestamp", "signal_bb"]], on="timestamp", how="inner")

    return df

def generate_combined_signal(df):
    def score(row):
        s = 0
        s += 1 if row["signal_rsi"] == "BUY" else -1 if row["signal_rsi"] == "SELL" else 0
        s += 1 if row["signal_ema"] == "BUY" else -1 if row["signal_ema"] == "SELL" else 0
        s += 1 if row["signal_bb"] == "BUY" else -1 if row["signal_bb"] == "SELL" else 0
        return s

    df["score"] = df.apply(score, axis=1)

    def final_signal(row):
        if row["score"] >= 2:
            return "BUY"
        elif row["score"] <= -2:
            return "SELL"
        else:
            return "WAIT"

    df["combined_signal"] = df.apply(final_signal, axis=1)
    return df

def simulate_portfolio(df, starting_balance=1000):
    balance = starting_balance
    position = 0  # 0: no position, 1: long
    portfolio_values = []

    for _, row in df.iterrows():
        price = row["close"]
        signal = row["combined_signal"]

        if signal == "BUY" and position == 0:
            buy_price = price
            position = 1
        elif signal == "SELL" and position == 1:
            pnl = (price - buy_price) / buy_price
            balance *= (1 + pnl)
            position = 0

        portfolio_values.append(balance)

    df["portfolio_value"] = portfolio_values
    return df

def plot_and_summary(df):
    final_value = df["portfolio_value"].iloc[-1]
    max_value = df["portfolio_value"].max()
    min_value = df["portfolio_value"].min()

    # Plot
    plt.figure(figsize=(14, 6))
    plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.title("📊 Combined Strategy Portfolio Performance")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(PLOT_DIR, "portfolio_plot_combined_strategy.png"))
    plt.close()

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_value:.2f} | Min: ${min_value:.2f}")
    print("📊 Grafik kaydedildi:", os.path.join(PLOT_DIR, "portfolio_plot_combined_strategy.png"))

# Kullanım örneği
if __name__ == "__main__":
    df = fetch_all()
    df = generate_combined_signal(df)
    df = simulate_portfolio(df)
    plot_and_summary(df)
    print(df[["timestamp", "signal_rsi", "signal_ema", "signal_bb", "score", "combined_signal", "portfolio_value"]].tail(20))
