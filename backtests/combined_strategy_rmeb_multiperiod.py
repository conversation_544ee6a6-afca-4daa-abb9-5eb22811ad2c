import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime

from strategies.UPDATED_rsi_macd import fetch_rsi_macd
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_bollinger import fetch_bollinger

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")
client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

# <PERSON>u<PERSON> klasörleri
LOG_DIR = "backtest_results/logs"
PLOT_DIR = "backtest_results/plots"
os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(PLOT_DIR, exist_ok=True)

def fetch_combined_signals(symbol="BTCUSDT", interval="15m", start_time=None, end_time=None):
    df_rsi = fetch_rsi_macd(symbol=symbol, interval=interval, lookback_days=None, start_time=start_time, end_time=end_time)
    df_rsi = df_rsi.rename(columns={"signal": "signal_rsi"})

    df_ema = fetch_ema_crossover(symbol=symbol, interval=interval, lookback_days=None, start_time=start_time, end_time=end_time)
    df_ema = df_ema.rename(columns={"signal": "signal_ema"})

    df_bb = fetch_bollinger(symbol=symbol, interval=interval, lookback_days=None, start_time=start_time, end_time=end_time)
    df_bb = df_bb.rename(columns={"signal": "signal_bb"})

    df = df_rsi.merge(df_ema[["timestamp", "signal_ema"]], on="timestamp", how="inner")
    df = df.merge(df_bb[["timestamp", "signal_bb"]], on="timestamp", how="inner")

    def score(row):
        s = 0
        s += 1 if row["signal_rsi"] == "BUY" else -1 if row["signal_rsi"] == "SELL" else 0
        s += 1 if row["signal_ema"] == "BUY" else -1 if row["signal_ema"] == "SELL" else 0
        s += 1 if row["signal_bb"] == "BUY" else -1 if row["signal_bb"] == "SELL" else 0
        return s

    df["score"] = df.apply(score, axis=1)

    def combined_signal(row):
        if row["score"] >= 2:
            return "BUY"
        elif row["score"] <= -2:
            return "SELL"
        else:
            return "WAIT"

    df["signal"] = df.apply(combined_signal, axis=1)
    return df

def simulate_trades(df, initial_balance=1000, fee_rate=0.001):
    balance = initial_balance
    position = 0
    portfolio = []
    log_lines = []

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and balance > 0:
            position = (balance * (1 - fee_rate)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f}")
        elif signal == "SELL" and position > 0:
            balance = (position * price) * (1 - fee_rate)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f}")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio
    return df, portfolio[-1], max(portfolio), min(portfolio), log_lines

# Test dönemleri
time_ranges = {
    "2022_Q1": (datetime(2022, 1, 1), datetime(2022, 4, 1)),
    "2023_Q2": (datetime(2023, 4, 1), datetime(2023, 7, 1)),
    "2024_Q3": (datetime(2024, 7, 1), datetime(2024, 10, 1)),
}

for label, (start, end) in time_ranges.items():
    print(f"\n🔁 Testing period: {label} ({start.date()} - {end.date()})")

    df = fetch_combined_signals(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=start, end_time=end)
    if df.empty:
        print("⚠️ No data fetched!")
        continue

    print(df["signal"].value_counts())
    df, final_value, max_value, min_value, logs = simulate_trades(df)

    # Log dosyası
    log_path = os.path.join(LOG_DIR, f"backtest_log_{label}_combined.txt")
    with open(log_path, "w") as f:
        f.write("\n".join(logs))

    # Grafik
    plt.figure(figsize=(14, 6))
    plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.title(f"📈 Combined Strategy Backtest: {label}")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(PLOT_DIR, f"portfolio_plot_{label}_combined.png"))
    plt.close()

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_value:.2f} | Min: ${min_value:.2f}")
    print(f"📁 Log saved to {log_path} | 📊 Chart saved to portfolio_plot_{label}_combined.png")