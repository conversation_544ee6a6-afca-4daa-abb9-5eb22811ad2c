import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime, timedelta, timezone

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_ohlcv_full(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=90):
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=lookback_days)

    # UNIX timestamp in milliseconds
    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)

        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1  # +1 ms to avoid duplication

    return pd.concat(df_list, ignore_index=True)


def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    avg_gain = pd.Series(gain).rolling(window=period).mean()
    avg_loss = pd.Series(loss).rolling(window=period).mean()
    rs = avg_gain / (avg_loss + 1e-9)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    signal_line = macd.ewm(span=signal).mean()
    histogram = macd - signal_line
    return macd, signal_line, histogram

def generate_signals(df):
    df["rsi"] = calculate_rsi(df["close"])
    df["macd"], df["macd_signal"], df["macd_hist"] = calculate_macd(df["close"])
    df["signal"] = "WAIT"
    df.loc[(df["rsi"] < 30) & (df["macd"] > df["macd_signal"]) & (df["macd_hist"] > 0), "signal"] = "BUY"
    df.loc[(df["rsi"] > 70) & (df["macd"] < df["macd_signal"]) & (df["macd_hist"] < 0), "signal"] = "SELL"
    return df

def simulate_trades(df, initial_balance=1000, fee_rate=0.001):
    balance = initial_balance
    position = 0
    portfolio = []

    log_lines = []

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and balance > 0:
            # Fee düşülerek alınabilecek coin miktarı
            position = (balance * (1 - fee_rate)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f} (with fee)")
        elif signal == "SELL" and position > 0:
            # Satışta fee uygulanıyor
            balance = position * price * (1 - fee_rate)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f} (with fee)")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio

    # Logları dosyaya yaz
    with open("backtest_log.txt", "w") as f:
        f.write("\n".join(log_lines))

    return df, portfolio[-1], max(portfolio), min(portfolio)


# RUN
df = fetch_ohlcv_full()
df = generate_signals(df)
df, final_value, max_value, min_value = simulate_trades(df)

# Sonuçlar
print(f"\n💼 Final Portfolio Value: ${final_value:.2f}")
print(f"📈 Max Portfolio Value: ${max_value:.2f}")
print(f"📉 Min Portfolio Value: ${min_value:.2f}")

# Plot
plt.figure(figsize=(14, 6))
plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
plt.xlabel("Time")
plt.ylabel("Portfolio Value ($)")
plt.title("📈 RSI + MACD Strategy Backtest (Last 3 Months)")
plt.legend()
plt.grid(True)
plt.tight_layout()
plt.savefig("portfolio_value_plot.png")
plt.show()
