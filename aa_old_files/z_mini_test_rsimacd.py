#!/usr/bin/env python3
import os
import numpy  as np
import pandas as pd
from datetime import datetime, timedelta, timezone

from strategies.stochastic_rsi   import fetch_ohlcv
from strategies.UPDATED_rsi_macd import compute_rsi, compute_macd

def compute_atr(closes, period=14):
    tr = closes.diff().abs()
    return tr.rolling(window=period, min_periods=1).mean()

def backtest_atr(signals, init_balance, fee, slip,
                 stop_mult=1.0, take_mult=2.0, max_trades=1000):
    df = signals.reset_index(drop=True).copy()
    df["atr"] = compute_atr(df["close"])
    bal, pos, entry = init_balance, 0.0, 0.0
    sl, tp = 0.0, 0.0
    trades, in_trade = [], False

    for _, row in df.iterrows():
        price, sig, atr = row["close"], row["signal"], row["atr"]
        if not in_trade and sig=="BUY":
            exec_p = price*(1+slip)
            qty    = bal/(exec_p*(1+fee))
            cost   = qty*exec_p
            commission = cost*fee
            if cost+commission <= bal:
                bal   -= cost+commission
                pos    = qty
                entry  = exec_p
                sl     = entry - stop_mult*atr
                tp     = entry + take_mult*atr
                in_trade = True

        elif in_trade:
            exit_p = None
            if price <= sl:
                exit_p = sl*(1-slip)
            elif price >= tp:
                exit_p = tp*(1-slip)
            elif sig=="SELL":
                exit_p = price*(1-slip)

            if exit_p is not None:
                proceeds    = pos*exit_p
                commission = proceeds*fee
                pnl         = proceeds - commission - (pos*entry)
                bal        += proceeds-commission
                trades.append(pnl)
                pos, in_trade = 0.0, False

    # equity curve
    equity = []
    for price in df["close"]:
        equity.append(bal + (pos*price if in_trade else 0))
    return equity, trades

def compute_metrics(equity, initial, interval):
    arr  = np.array(equity)
    rets = arr[1:]/arr[:-1] - 1
    bars = 1440/int(interval[:-1])
    mu   = np.nanmean(rets*bars)
    sd   = np.nanstd(rets*bars)
    sharpe = (mu*252)/(sd*np.sqrt(252)) if sd else np.nan
    total  = arr[-1]/initial - 1
    dd     = np.min((arr - np.maximum.accumulate(arr))/np.maximum.accumulate(arr))
    return total, sharpe, dd

def trade_stats(pnls):
    if not pnls: return dict(n_trades=0, win_rate=np.nan, expectancy=np.nan)
    arr   = np.array(pnls)
    wins  = arr[arr>0]; losses = arr[arr<0]
    wr    = len(wins)/len(arr)
    aw    = wins.mean()    if wins.size   else 0
    al    = losses.mean()  if losses.size else 0
    exp   = wr*aw + (1-wr)*al
    return dict(n_trades=len(arr), win_rate=wr, expectancy=exp)

def gen_signals(raw, r_lo, r_hi, m_fast, m_slow, m_sig):
    df = raw.copy().reset_index(drop=True)
    df["rsi"]              = compute_rsi(df["close"], period=r_lo)
    macd, sig_line         = compute_macd(
                                df["close"],
                                short_period=m_fast,
                                long_period =m_slow,
                                signal_period=m_sig
                            )
    df["macd"], df["sig_l"] = macd, sig_line
    df["prev_macd"]        = df["macd"].shift()
    df["prev_sig"]         = df["sig_l"].shift()
    df["signal"] = "WAIT"
    buy_mask  = (df["rsi"]<r_lo) & (df["macd"]>df["sig_l"]) & (df["prev_macd"]<=df["prev_sig"])
    sell_mask = (df["rsi"]>r_hi) & (df["macd"]<df["sig_l"]) & (df["prev_macd"]>=df["prev_sig"])
    df.loc[buy_mask,  "signal"]="BUY"
    df.loc[sell_mask, "signal"]="SELL"
    return df[["timestamp","close","signal"]]

if __name__=="__main__":
    # top‑3 raw combos found
    combos = [
      dict(r_lo=30, r_hi=75, m_fast=6,  m_slow=12, m_sig=9),
      dict(r_lo=30, r_hi=75, m_fast=8,  m_slow=17, m_sig=9),
      dict(r_lo=35, r_hi=75, m_fast=6,  m_slow=12, m_sig=9),
    ]

    symbol, interval = "BTCUSDT","15m"
    start = datetime(2023,1,1,tzinfo=timezone.utc)
    end   = datetime(2023,7,1,tzinfo=timezone.utc)
    raw   = fetch_ohlcv(symbol,interval,start_time=start,end_time=end)
    raw["timestamp"]=pd.to_datetime(raw["timestamp"],utc=True)

    # walk‑forward settings
    IS,OS,STEP = 120,14,7
    init_cap,fee,slip,stop_mult,take_mult = 1000,0.001,0.001,1.0,2.0

    for c in combos:
        print(f"\n=== Combo: RSI<{c['r_lo']}/>{c['r_hi']}  "
              f"MACD {c['m_fast']}-{c['m_slow']}-{c['m_sig']} ===")
        rows, cur = [], start
        while cur + timedelta(days=IS+OS) <= end:
            train_e = cur + timedelta(days=IS)
            test_s  = train_e
            test_e  = train_e + timedelta(days=OS)
            window  = raw[(raw.timestamp>=cur)&(raw.timestamp<test_e)]
            train_w = window[window.timestamp<train_e]
            test_w  = window[window.timestamp>=train_e]

            all_w   = pd.concat([train_w, test_w], ignore_index=True)

            sig_oos = gen_signals(
                all_w,
                r_lo    = c['r_lo'],
                r_hi    = c['r_hi'],
                m_fast  = c['m_fast'],
                m_slow  = c['m_slow'],
                m_sig   = c['m_sig']
            )
            
            eq_oos, tr_oos = backtest_atr(sig_oos, init_cap,fee,slip,
                                          stop_mult,take_mult, max_trades=1000)
            tot, shr, dd = compute_metrics(eq_oos, init_cap, interval)
            st = trade_stats(tr_oos)
            rows.append({
              "test_start": test_s.date(),
              "test_end":   test_e.date(),
              "return":     tot,
              "sharpe":     shr,
              "max_dd":     dd,
              **st
            })
            cur += timedelta(days=STEP)

        df = pd.DataFrame(rows)
        print(df.to_string(index=False))
