#!/usr/bin/env python3
"""
Enhanced Multi-Strategy Trading System
Addresses key issues in the original implementation:
1. Better signal filtering and quality
2. Adaptive risk management
3. Market regime detection
4. Position sizing optimization
5. Advanced backtesting with realistic costs
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import your existing strategy modules
from strategies.UPDATED_bollinger import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd import fetch_rsi_macd
from strategies.supertrend import fetch_supertrend
from strategies.stochastic_rsi import fetch_stoch_rsi

class MarketRegimeDetector:
    """Detect market regimes to filter strategies appropriately"""
    
    @staticmethod
    def detect_regime(df: pd.DataFrame, lookback: int = 50) -> pd.Series:
        """
        Detect market regime: trending, ranging, volatile
        Returns: Series with regime labels
        """
        # Calculate volatility (normalized ATR)
        df['atr'] = df['close'].rolling(14).apply(
            lambda x: np.mean(np.abs(x.diff()))
        )
        df['volatility'] = df['atr'] / df['close']
        
        # Calculate trend strength using ADX-like measure
        df['hl_spread'] = (df['high'] - df['low']) / df['close']
        df['trend_strength'] = df['close'].rolling(lookback).apply(
            lambda x: abs(x.iloc[-1] - x.iloc[0]) / (x.std() * np.sqrt(len(x)))
        )
        
        # Calculate range-bound indicator
        df['range_indicator'] = df['close'].rolling(lookback).apply(
            lambda x: (x.max() - x.min()) / x.mean()
        )
        
        # Regime classification
        regime = pd.Series(index=df.index, dtype='str')
        
        # High volatility regime
        vol_threshold = df['volatility'].quantile(0.7)
        regime[df['volatility'] > vol_threshold] = 'volatile'
        
        # Trending regime
        trend_threshold = df['trend_strength'].quantile(0.6)
        range_threshold = df['range_indicator'].quantile(0.4)
        
        trending_mask = (
            (df['trend_strength'] > trend_threshold) & 
            (df['range_indicator'] > range_threshold) &
            (regime.isna())
        )
        regime[trending_mask] = 'trending'
        
        # Range-bound regime (default)
        regime = regime.fillna('ranging')
        
        return regime

class SignalProcessor:
    """Enhanced signal processing with filtering and quality scoring"""
    
    @staticmethod
    def filter_signals(df: pd.DataFrame, min_strength: float = 0.3) -> pd.DataFrame:
        """Apply signal filtering and quality scoring"""
        df = df.copy()
        
        # Calculate signal strength based on multiple factors
        df['signal_strength'] = 0.0
        
        # RSI divergence strength
        if 'rsi' in df.columns:
            rsi_extreme = np.where(df['rsi'] < 30, 1-df['rsi']/30, 
                                 np.where(df['rsi'] > 70, (df['rsi']-70)/30, 0))
            df['signal_strength'] += rsi_extreme * 0.3
        
        # MACD momentum
        if 'macd' in df.columns and 'signal_line' in df.columns:
            macd_momentum = np.abs(df['macd'] - df['signal_line']) / df['close'].rolling(20).std()
            df['signal_strength'] += np.clip(macd_momentum, 0, 1) * 0.3
        
        # Volume confirmation (if available)
        if 'volume' in df.columns:
            volume_ma = df['volume'].rolling(20).mean()
            volume_factor = np.clip(df['volume'] / volume_ma, 0.5, 2.0)
            df['signal_strength'] *= volume_factor
        
        # Volatility adjustment
        if 'atr' in df.columns:
            volatility_factor = np.clip(df['atr'] / df['atr'].rolling(50).mean(), 0.5, 2.0)
            df['signal_strength'] *= (2.0 - volatility_factor)  # Inverse relationship
        
        # Filter weak signals
        weak_signals = df['signal_strength'] < min_strength
        df.loc[weak_signals, 'signal'] = 'WAIT'
        
        return df
    
    @staticmethod
    def add_confluence_scoring(df: pd.DataFrame) -> pd.DataFrame:
        """Add confluence scoring for multiple timeframe analysis"""
        df = df.copy()
        
        # Short-term momentum (5-period)
        df['momentum_short'] = df['close'].pct_change(5)
        
        # Medium-term momentum (20-period)
        df['momentum_medium'] = df['close'].pct_change(20)
        
        # Long-term momentum (50-period)
        df['momentum_long'] = df['close'].pct_change(50)
        
        # Support/Resistance levels
        df['support'] = df['close'].rolling(20).min()
        df['resistance'] = df['close'].rolling(20).max()
        df['sr_position'] = (df['close'] - df['support']) / (df['resistance'] - df['support'])
        
        # Confluence score
        confluence_factors = []
        
        # Momentum alignment
        momentum_alignment = (
            np.sign(df['momentum_short']) == np.sign(df['momentum_medium'])
        ).astype(int)
        confluence_factors.append(momentum_alignment * 0.3)
        
        # Support/Resistance proximity
        sr_factor = np.where(df['sr_position'] < 0.2, 1.0,  # Near support
                            np.where(df['sr_position'] > 0.8, -1.0, 0.0))  # Near resistance
        confluence_factors.append(sr_factor * 0.2)
        
        df['confluence_score'] = sum(confluence_factors)
        
        return df

class AdaptiveRiskManager:
    """Adaptive risk management with dynamic position sizing"""
    
    @staticmethod
    def calculate_position_size(df: pd.DataFrame, capital: float, 
                              risk_per_trade: float = 0.02) -> pd.Series:
        """
        Calculate adaptive position size based on volatility and confidence
        """
        # Base position size
        base_size = capital * risk_per_trade
        
        # Volatility adjustment
        volatility = df['close'].rolling(14).std() / df['close']
        vol_factor = np.clip(1.0 / (volatility * 10), 0.5, 2.0)
        
        # Confidence adjustment (if signal_strength available)
        if 'signal_strength' in df.columns:
            confidence_factor = np.clip(df['signal_strength'] * 2, 0.5, 2.0)
        else:
            confidence_factor = 1.0
        
        # Market regime adjustment
        if 'regime' in df.columns:
            regime_factor = df['regime'].map({
                'trending': 1.2,
                'ranging': 0.8,
                'volatile': 0.6
            })
        else:
            regime_factor = 1.0
        
        position_size = base_size * vol_factor * confidence_factor * regime_factor
        
        return position_size
    
    @staticmethod
    def adaptive_stops(df: pd.DataFrame, entry_price: float, 
                      signal_type: str, base_atr_mult: float = 1.5) -> Dict:
        """
        Calculate adaptive stop-loss and take-profit levels
        """
        current_atr = df['atr'].iloc[-1]
        current_vol = df['close'].rolling(14).std().iloc[-1] / df['close'].iloc[-1]
        
        # Adjust multiplier based on volatility
        vol_adjustment = np.clip(current_vol / 0.02, 0.5, 2.0)  # Normalize to 2% base vol
        
        # Adjust based on market regime
        if 'regime' in df.columns:
            regime = df['regime'].iloc[-1]
            regime_adjustment = {
                'trending': 1.2,
                'ranging': 0.8,
                'volatile': 1.5
            }.get(regime, 1.0)
        else:
            regime_adjustment = 1.0
        
        adjusted_mult = base_atr_mult * vol_adjustment * regime_adjustment
        
        if signal_type == 'BUY':
            stop_loss = entry_price - (adjusted_mult * current_atr)
            take_profit = entry_price + (adjusted_mult * current_atr * 2)  # 2:1 R/R
        else:
            stop_loss = entry_price + (adjusted_mult * current_atr)
            take_profit = entry_price - (adjusted_mult * current_atr * 2)
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'atr_mult': adjusted_mult
        }

class EnhancedBacktester:
    """Enhanced backtesting with realistic transaction costs and slippage"""
    
    def __init__(self, initial_capital: float = 10000, 
                 base_fee: float = 0.001, min_trade_size: float = 10):
        self.initial_capital = initial_capital
        self.base_fee = base_fee
        self.min_trade_size = min_trade_size
        
    def calculate_slippage(self, price: float, volume: float, 
                          market_impact: float = 0.0001) -> float:
        """Calculate realistic slippage based on order size"""
        # Simple market impact model
        normalized_volume = volume / 1000000  # Normalize to millions
        slippage = market_impact * np.sqrt(normalized_volume)
        return min(slippage, 0.005)  # Cap at 0.5%
    
    def calculate_fees(self, trade_value: float, is_maker: bool = False) -> float:
        """Calculate trading fees with maker/taker distinction"""
        fee_rate = self.base_fee * (0.8 if is_maker else 1.0)
        return trade_value * fee_rate
    
    def backtest_strategy(self, signals_df: pd.DataFrame, 
                         risk_manager: AdaptiveRiskManager) -> Dict:
        """
        Enhanced backtesting with adaptive risk management
        """
        df = signals_df.copy()
        
        # Initialize tracking variables
        capital = self.initial_capital
        position = 0.0
        entry_price = 0.0
        trades = []
        equity_curve = []
        
        in_trade = False
        stop_loss = 0.0
        take_profit = 0.0
        
        for i, row in df.iterrows():
            current_price = row['close']
            signal = row['signal']
            
            # Calculate position size
            position_size = risk_manager.calculate_position_size(
                df.iloc[:i+1], capital, risk_per_trade=0.02
            ).iloc[-1]
            
            # Entry logic
            if not in_trade and signal in ['BUY', 'SELL']:
                # Check minimum trade size
                trade_value = position_size
                if trade_value < self.min_trade_size:
                    equity_curve.append(capital)
                    continue
                
                # Calculate slippage and fees
                slippage = self.calculate_slippage(current_price, trade_value)
                
                if signal == 'BUY':
                    execution_price = current_price * (1 + slippage)
                    position = trade_value / execution_price
                else:
                    execution_price = current_price * (1 - slippage)
                    position = -trade_value / execution_price
                
                fees = self.calculate_fees(trade_value)
                
                # Update capital and position
                capital -= (trade_value + fees)
                entry_price = execution_price
                in_trade = True
                
                # Set adaptive stops
                stops = risk_manager.adaptive_stops(
                    df.iloc[:i+1], entry_price, signal
                )
                stop_loss = stops['stop_loss']
                take_profit = stops['take_profit']
            
            # Exit logic
            elif in_trade:
                exit_triggered = False
                exit_price = current_price
                exit_reason = 'signal'
                
                # Check stops
                if position > 0:  # Long position
                    if current_price <= stop_loss:
                        exit_price = stop_loss
                        exit_reason = 'stop_loss'
                        exit_triggered = True
                    elif current_price >= take_profit:
                        exit_price = take_profit
                        exit_reason = 'take_profit'
                        exit_triggered = True
                elif position < 0:  # Short position
                    if current_price >= stop_loss:
                        exit_price = stop_loss
                        exit_reason = 'stop_loss'
                        exit_triggered = True
                    elif current_price <= take_profit:
                        exit_price = take_profit
                        exit_reason = 'take_profit'
                        exit_triggered = True
                
                # Check signal exit
                if not exit_triggered and signal == 'SELL' and position > 0:
                    exit_triggered = True
                elif not exit_triggered and signal == 'BUY' and position < 0:
                    exit_triggered = True
                
                if exit_triggered:
                    # Calculate exit slippage and fees
                    trade_value = abs(position * exit_price)
                    slippage = self.calculate_slippage(exit_price, trade_value)
                    
                    if position > 0:
                        final_exit_price = exit_price * (1 - slippage)
                    else:
                        final_exit_price = exit_price * (1 + slippage)
                    
                    fees = self.calculate_fees(trade_value)
                    
                    # Calculate P&L
                    if position > 0:
                        pnl = position * (final_exit_price - entry_price) - fees
                    else:
                        pnl = abs(position) * (entry_price - final_exit_price) - fees
                    
                    # Update capital
                    capital += (trade_value - fees)
                    
                    # Record trade
                    trades.append({
                        'entry_price': entry_price,
                        'exit_price': final_exit_price,
                        'position_size': abs(position),
                        'pnl': pnl,
                        'exit_reason': exit_reason,
                        'timestamp': row['timestamp']
                    })
                    
                    # Reset position
                    position = 0.0
                    in_trade = False
            
            # Update equity curve
            if in_trade:
                unrealized_pnl = position * (current_price - entry_price) if position > 0 else abs(position) * (entry_price - current_price)
                current_equity = capital + unrealized_pnl
            else:
                current_equity = capital
            
            equity_curve.append(current_equity)
        
        return {
            'final_capital': capital,
            'trades': trades,
            'equity_curve': equity_curve,
            'total_return': (capital - self.initial_capital) / self.initial_capital,
            'num_trades': len(trades)
        }

class EnhancedMultiStrategy:
    """Main strategy orchestrator with enhanced features"""
    
    def __init__(self):
        self.regime_detector = MarketRegimeDetector()
        self.signal_processor = SignalProcessor()
        self.risk_manager = AdaptiveRiskManager()
        self.backtester = EnhancedBacktester()
        
    def fetch_enhanced_data(self, symbol: str, interval: str, 
                           start_time: datetime, end_time: datetime) -> Dict:
        """Fetch and enhance data from all strategies"""
        
        # Fetch base data
        strategies = {
            "bollinger": fetch_bollinger(symbol, interval, start_time=start_time, end_time=end_time),
            "ema_crossover": fetch_ema_crossover(symbol, interval, start_time=start_time, end_time=end_time),
            "rsi_macd": fetch_rsi_macd(symbol, interval, start_time=start_time, end_time=end_time),
            "supertrend": fetch_supertrend(symbol, interval, start_time=start_time, end_time=end_time),
            "stochastic_rsi": fetch_stoch_rsi(symbol, interval, start_time=start_time, end_time=end_time)
        }
        
        # Enhance each strategy's data
        enhanced_strategies = {}
        for name, df in strategies.items():
            # === STEP 1: normalize all column names to lowercase ===
            df.columns = [c.lower() for c in df.columns]

            # === STEP 2: sanity‐check that OHLC columns exist ===
            missing = [col for col in ('high','low','close') if col not in df.columns]
            if missing:
                raise ValueError(f"[{name}] missing required columns: {missing}")

            # === STEP 3: now it's safe to calculate ATR & regimes ===
            df['atr']    = df['close'].rolling(14).apply(lambda x: np.mean(np.abs(x.diff())))
            df['regime'] = self.regime_detector.detect_regime(df)
            
            # Filter signals and add confluence
            df = self.signal_processor.filter_signals(df)
            df = self.signal_processor.add_confluence_scoring(df)
            
            enhanced_strategies[name] = df
        
        return enhanced_strategies
    
    def create_ensemble_signal(self, strategies: Dict, weights: Dict, 
                             regime_filters: Dict = None) -> pd.DataFrame:
        """Create ensemble signal with regime-based filtering"""
        
        # Merge all strategies
        base_df = None
        for name, df in strategies.items():
            cols = ['timestamp', 'close', 'signal', 'signal_strength', 'regime', 'confluence_score']
            temp_df = df[cols].copy()
            temp_df = temp_df.rename(columns={
                'signal': f'signal_{name}',
                'signal_strength': f'strength_{name}',
                'confluence_score': f'confluence_{name}'
            })
            
            if base_df is None:
                base_df = temp_df
            else:
                base_df = base_df.merge(temp_df, on=['timestamp', 'close', 'regime'], how='outer')
        
        # Apply regime filters
        if regime_filters:
            for strategy, allowed_regimes in regime_filters.items():
                mask = ~base_df['regime'].isin(allowed_regimes)
                base_df.loc[mask, f'signal_{strategy}'] = 'WAIT'
                base_df.loc[mask, f'strength_{strategy}'] = 0.0
        
        # Calculate weighted ensemble score
        base_df['ensemble_score'] = 0.0
        
        for strategy, weight in weights.items():
            if f'signal_{strategy}' in base_df.columns:
                signal_numeric = base_df[f'signal_{strategy}'].map({'BUY': 1, 'SELL': -1, 'WAIT': 0})
                strength = base_df[f'strength_{strategy}'].fillna(0.5)
                confluence = base_df[f'confluence_{strategy}'].fillna(0.0)
                
                # Combined score with strength and confluence
                strategy_score = signal_numeric * strength * (1 + confluence)
                base_df['ensemble_score'] += strategy_score * weight
        
        # Generate final signal
        base_df['final_signal'] = 'WAIT'
        
        # Dynamic thresholds based on market regime
        regime_thresholds = {
            'trending': 0.3,
            'ranging': 0.4,
            'volatile': 0.5
        }
        
        for regime, threshold in regime_thresholds.items():
            regime_mask = base_df['regime'] == regime
            base_df.loc[regime_mask & (base_df['ensemble_score'] > threshold), 'final_signal'] = 'BUY'
            base_df.loc[regime_mask & (base_df['ensemble_score'] < -threshold), 'final_signal'] = 'SELL'
        
        base_df = base_df.rename(columns={'final_signal': 'signal'})
        return base_df[['timestamp', 'close', 'signal', 'ensemble_score', 'regime']]

# Configuration for enhanced strategy
def get_enhanced_config():
    """Get enhanced strategy configuration"""
    return {
        'regime_filters': {
            'bollinger': ['ranging', 'volatile'],
            'ema_crossover': ['trending'],
            'rsi_macd': ['ranging', 'volatile'],
            'supertrend': ['trending'],
            'stochastic_rsi': ['ranging', 'volatile']
        },
        'adaptive_weights': {
            'bollinger': 0.20,
            'ema_crossover': 0.25,
            'rsi_macd': 0.25,
            'supertrend': 0.20,
            'stochastic_rsi': 0.10
        },
        'risk_params': {
            'base_risk_per_trade': 0.015,  # 1.5% per trade
            'max_position_risk': 0.06,     # 6% max portfolio risk
            'base_atr_multiplier': 1.5,
            'max_trades_per_day': 3
        }
    }

if __name__ == "__main__":
    # Test the enhanced strategy
    strategy = EnhancedMultiStrategy()
    config = get_enhanced_config()
    
    # Test parameters
    symbol = "BTCUSDT"
    interval = "15m"
    start_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    end_time = datetime(2023, 3, 1, tzinfo=timezone.utc)
    
    print("🚀 Testing Enhanced Multi-Strategy System...")
    
    # Fetch enhanced data
    strategies = strategy.fetch_enhanced_data(symbol, interval, start_time, end_time)
    
    # Create ensemble signal
    ensemble_df = strategy.create_ensemble_signal(
        strategies, 
        config['adaptive_weights'],
        config['regime_filters']
    )
    
    # Run backtest
    results = strategy.backtester.backtest_strategy(ensemble_df, strategy.risk_manager)
    
    print(f"📊 Results:")
    print(f"Total Return: {results['total_return']:.2%}")
    print(f"Number of Trades: {results['num_trades']}")
    print(f"Final Capital: ${results['final_capital']:.2f}")
    
    if results['trades']:
        winning_trades = [t for t in results['trades'] if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(results['trades'])
        avg_win = np.mean([t['pnl'] for t in winning_trades]) if winning_trades else 0
        avg_loss = np.mean([t['pnl'] for t in results['trades'] if t['pnl'] < 0])
        
        print(f"Win Rate: {win_rate:.1%}")
        print(f"Average Win: ${avg_win:.2f}")
        print(f"Average Loss: ${avg_loss:.2f}")
        print(f"Profit Factor: {abs(avg_win / avg_loss) if avg_loss < 0 else 'N/A'}")