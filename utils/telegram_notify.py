# utils/telegram_notify.py

import requests
from config.env import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID

def send_telegram_message(message):
    url = f"https://api.telegram.org/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    data = {"chat_id": TELEGRAM_CHAT_ID, "text": message}
    response = requests.post(url, data=data)
    
    # Debug çıktısı
    print("Telegram API yanıtı:", response.status_code)
    print("Cevap içeriği:", response.text)
    
    return response
