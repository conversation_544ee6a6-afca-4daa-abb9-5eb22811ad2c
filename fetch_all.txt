def fetch_all(symbol, interval, start, end):
    return {
      "bollinger":      fetch_bollinger(   symbol=symbol, interval=interval, start_time=start, end_time=end),
      "ema_crossover":  fetch_ema_crossover(symbol=symbol, interval=interval, start_time=start, end_time=end),
      "rsi_macd":       fetch_rsi_macd(     symbol=symbol, interval=interval, start_time=start, end_time=end),
      "supertrend":     fetch_supertrend(   symbol=symbol, interval=interval, start_time=start, end_time=end),
      "stochastic_rsi": fetch_stoch_rsi(    symbol=symbol, interval=interval, start_time=start, end_time=end),
    }