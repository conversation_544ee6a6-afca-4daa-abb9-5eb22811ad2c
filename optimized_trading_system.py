#!/usr/bin/env python3
"""
Optimized Trading System with Better Parameters
Fine-tuned for crypto market conditions
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

from ultimate_trading_system import UltimateTradingSystem

class OptimizedTradingSystem(UltimateTradingSystem):
    """
    Optimized version with better parameters for crypto trading
    """
    
    def __init__(self, initial_capital: float = 10000, 
                 risk_per_trade: float = 0.015,
                 max_positions: int = 3):
        
        super().__init__(initial_capital, risk_per_trade, max_positions)
        
        # More aggressive strategy configurations for crypto
        self.strategy_configs = {
            'crypto_conservative': {
                'bollinger': 0.40,
                'rsi_macd': 0.35,
                'stochastic_rsi': 0.25
            },
            'crypto_aggressive': {
                'advanced_momentum': 0.45,
                'ema_crossover': 0.35,
                'supertrend': 0.20
            },
            'crypto_balanced': {
                'advanced_momentum': 0.30,
                'bollinger': 0.25,
                'rsi_macd': 0.20,
                'ema_crossover': 0.15,
                'supertrend': 0.10
            },
            'momentum_focused': {
                'advanced_momentum': 0.50,
                'rsi_macd': 0.30,
                'ema_crossover': 0.20
            }
        }
        
        # More aggressive thresholds for crypto volatility
        self.regime_thresholds = {
            'trending': 0.10,   # Very low threshold for trending
            'ranging': 0.15,    # Low threshold for ranging
            'volatile': 0.20    # Moderate threshold for volatile
        }
    
    def detect_market_regime(self, df: pd.DataFrame) -> str:
        """
        Enhanced market regime detection for crypto
        """
        if len(df) < 20:
            return 'ranging'
        
        # Calculate multiple trend indicators
        close = df['close']
        
        # Short-term trend (EMA 8 vs EMA 21)
        ema_8 = close.ewm(span=8).mean()
        ema_21 = close.ewm(span=21).mean()
        short_trend = (ema_8.iloc[-1] - ema_21.iloc[-1]) / ema_21.iloc[-1]
        
        # Medium-term trend (EMA 21 vs EMA 50)
        ema_50 = close.ewm(span=50).mean() if len(close) >= 50 else close.ewm(span=len(close)//2).mean()
        medium_trend = (ema_21.iloc[-1] - ema_50.iloc[-1]) / ema_50.iloc[-1]
        
        # Volatility (recent vs historical)
        returns = close.pct_change().dropna()
        if len(returns) >= 20:
            recent_vol = returns.tail(10).std()
            historical_vol = returns.std()
            vol_ratio = recent_vol / historical_vol if historical_vol > 0 else 1
        else:
            vol_ratio = 1
        
        # Price momentum
        if len(close) >= 10:
            momentum = (close.iloc[-1] - close.iloc[-10]) / close.iloc[-10]
        else:
            momentum = 0
        
        # Regime classification with crypto-specific logic
        trend_strength = abs(short_trend) + abs(medium_trend)
        
        if vol_ratio > 1.5:  # High volatility
            return 'volatile'
        elif trend_strength > 0.02 and abs(momentum) > 0.03:  # Strong trend + momentum
            return 'trending'
        else:
            return 'ranging'
    
    def generate_enhanced_signals(self, symbol: str, interval: str,
                                 start_time: datetime, end_time: datetime,
                                 config_name: str = 'crypto_balanced') -> pd.DataFrame:
        """
        Generate enhanced signals with better filtering
        """
        # Get base signals
        signals_df = self.generate_ensemble_signals(
            symbol, interval, start_time, end_time, config_name
        )
        
        if len(signals_df) == 0:
            return signals_df
        
        # Apply additional filters
        signals_df = self.apply_advanced_filters(signals_df)
        
        return signals_df
    
    def apply_advanced_filters(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Apply advanced signal filtering
        """
        df = df.copy()
        
        # Calculate additional indicators
        close = df['close']
        
        # Volume-weighted average price (simplified)
        df['vwap'] = close.rolling(20).mean()  # Simplified VWAP
        
        # Price relative to VWAP
        df['price_vs_vwap'] = (close - df['vwap']) / df['vwap']
        
        # Momentum filter
        df['momentum_5'] = close.pct_change(5)
        df['momentum_10'] = close.pct_change(10)
        
        # Volatility filter
        df['volatility'] = close.rolling(10).std() / close.rolling(10).mean()
        df['vol_percentile'] = df['volatility'].rolling(50).rank(pct=True)
        
        # Enhanced signal filtering
        original_signals = df['signal'].copy()
        
        # Filter 1: Momentum alignment
        momentum_aligned = (
            ((df['signal'] == 'BUY') & (df['momentum_5'] > -0.01) & (df['momentum_10'] > -0.02)) |
            ((df['signal'] == 'SELL') & (df['momentum_5'] < 0.01) & (df['momentum_10'] < 0.02)) |
            (df['signal'] == 'WAIT')
        )
        
        # Filter 2: Volatility filter (avoid extreme volatility)
        vol_filter = df['vol_percentile'] < 0.95
        
        # Filter 3: Price vs VWAP filter
        vwap_filter = (
            ((df['signal'] == 'BUY') & (df['price_vs_vwap'] > -0.02)) |
            ((df['signal'] == 'SELL') & (df['price_vs_vwap'] < 0.02)) |
            (df['signal'] == 'WAIT')
        )
        
        # Apply all filters
        filtered_signals = momentum_aligned & vol_filter & vwap_filter
        df.loc[~filtered_signals, 'signal'] = 'WAIT'
        
        # Adjust signal strength based on filters
        filter_score = (
            momentum_aligned.astype(int) * 0.4 +
            vol_filter.astype(int) * 0.3 +
            vwap_filter.astype(int) * 0.3
        )
        
        df['signal_strength'] = df['signal_strength'] * filter_score
        
        return df
    
    def run_optimized_backtest(self, symbol: str = "BTCUSDT", interval: str = "15m",
                              days_back: int = 90) -> Dict:
        """
        Run optimized backtest with multiple configurations
        """
        print(f"🚀 Running Optimized Backtest for {symbol}")
        print("=" * 50)
        
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days_back)
        
        results = {}
        
        # Test all configurations
        for config_name in self.strategy_configs.keys():
            print(f"  Testing {config_name}...")
            
            try:
                # Generate enhanced signals
                signals_df = self.generate_enhanced_signals(
                    symbol, interval, start_date, end_date, config_name
                )
                
                # Run backtest
                backtest_results = self.run_backtest(
                    symbol, interval, start_date, end_date, config_name
                )
                
                # Calculate score
                score = self._calculate_config_score(backtest_results)
                
                results[config_name] = {
                    'results': backtest_results,
                    'score': score,
                    'signals_generated': len(signals_df[signals_df['signal'] != 'WAIT'])
                }
                
                # Print quick summary
                print(f"    Return: {backtest_results.get('total_return', 0):.2%}, "
                      f"Trades: {backtest_results.get('num_trades', 0)}, "
                      f"Win Rate: {backtest_results.get('win_rate', 0):.1%}")
                
            except Exception as e:
                print(f"    Error: {e}")
                results[config_name] = {'error': str(e), 'score': -1000}
        
        # Find best configuration
        best_config = max(results.keys(), key=lambda k: results[k].get('score', -1000))
        
        print(f"\n✅ Best Configuration: {best_config}")
        print(f"Score: {results[best_config].get('score', 0):.1f}")
        
        return {
            'best_config': best_config,
            'all_results': results,
            'recommendation': self._get_recommendation(results[best_config])
        }
    
    def _get_recommendation(self, best_result: Dict) -> str:
        """
        Get trading recommendation based on results
        """
        if 'error' in best_result:
            return "❌ System needs debugging before live trading"
        
        results = best_result.get('results', {})
        total_return = results.get('total_return', 0)
        win_rate = results.get('win_rate', 0)
        max_drawdown = results.get('max_drawdown', 0)
        num_trades = results.get('num_trades', 0)
        
        if num_trades < 5:
            return "⚠️ Too few trades generated. Consider more aggressive thresholds."
        
        if total_return > 0.05 and win_rate > 0.4 and max_drawdown < 0.15:
            return "🚀 Excellent performance! Ready for live trading with small position sizes."
        
        elif total_return > 0 and win_rate > 0.35:
            return "✅ Good performance. Start with paper trading, then small live positions."
        
        elif total_return > -0.05 and win_rate > 0.3:
            return "⚠️ Marginal performance. Consider paper trading first and parameter tuning."
        
        else:
            return "❌ Poor performance. Needs optimization before live trading."

def main():
    """
    Run the optimized trading system
    """
    print("🚀 OPTIMIZED CRYPTO TRADING SYSTEM")
    print("=" * 60)
    
    # Initialize optimized system
    system = OptimizedTradingSystem(
        initial_capital=10000,
        risk_per_trade=0.015,
        max_positions=3
    )
    
    # Run comprehensive test
    results = system.run_optimized_backtest(
        symbol="BTCUSDT",
        interval="15m",
        days_back=120  # 4 months of data
    )
    
    # Display results
    best_config = results['best_config']
    best_results = results['all_results'][best_config]['results']
    
    print(f"\n" + "=" * 60)
    print("OPTIMIZED SYSTEM RESULTS")
    print("=" * 60)
    
    print(f"\n📊 Best Configuration: {best_config}")
    print(f"Total Return:     {best_results.get('total_return', 0):8.2%}")
    print(f"Sharpe Ratio:     {best_results.get('sharpe_ratio', 0):8.2f}")
    print(f"Max Drawdown:     {best_results.get('max_drawdown', 0):8.2%}")
    print(f"Win Rate:         {best_results.get('win_rate', 0):8.1%}")
    print(f"Total Trades:     {best_results.get('num_trades', 0):8d}")
    print(f"Profit Factor:    {best_results.get('profit_factor', 0):8.2f}")
    
    print(f"\n🎯 Recommendation:")
    print(results['recommendation'])
    
    # Configuration comparison
    print(f"\n📈 Configuration Comparison:")
    print(f"{'Config':<20} {'Return':<8} {'Trades':<7} {'Win Rate':<8} {'Score':<8}")
    print("-" * 60)
    
    for config, data in results['all_results'].items():
        if 'error' not in data:
            res = data['results']
            print(f"{config:<20} {res.get('total_return', 0):7.1%} "
                  f"{res.get('num_trades', 0):6d} "
                  f"{res.get('win_rate', 0):7.1%} "
                  f"{data.get('score', 0):7.1f}")
        else:
            print(f"{config:<20} ERROR")
    
    return results

if __name__ == "__main__":
    results = main()
