#!/usr/bin/env python3
"""
Enhanced Strategy System with Market Adaptation
Focuses on longer timeframes and better market condition detection
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from enhanced_backtester import EnhancedBacktester

# Import strategies
try:
    from strategies.UPDATED_rsi_macd import fetch_rsi_macd
    from strategies.UPDATED_ema_crossover import fetch_ema_crossover
    from strategies.UPDATED_bollinger import fetch_bollinger
    from strategies.supertrend import fetch_supertrend
    from strategies.stochastic_rsi import fetch_stoch_rsi
    from strategies.advanced_momentum_strategy import fetch_advanced_momentum
except ImportError as e:
    print(f"Warning: Some strategies not available: {e}")

class EnhancedStrategySystem:
    """
    Enhanced strategy system with better market adaptation
    """
    
    def __init__(self):
        self.strategies = {
            'rsi_macd': fetch_rsi_macd,
            'ema_crossover': fetch_ema_crossover,
            'bollinger': fetch_bollinger,
            'supertrend': fetch_supertrend,
            'stochastic_rsi': fetch_stoch_rsi,
            'advanced_momentum': fetch_advanced_momentum
        }
    
    def detect_market_condition(self, df: pd.DataFrame) -> str:
        """
        Enhanced market condition detection
        """
        if len(df) < 50:
            return 'uncertain'
        
        close = df['close']
        
        # Multiple trend indicators
        sma_20 = close.rolling(20).mean()
        sma_50 = close.rolling(50).mean()
        ema_12 = close.ewm(span=12).mean()
        ema_26 = close.ewm(span=26).mean()
        
        # Current price relative to moving averages
        price_vs_sma20 = (close.iloc[-1] - sma_20.iloc[-1]) / sma_20.iloc[-1]
        price_vs_sma50 = (close.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
        
        # Trend strength
        sma_trend = (sma_20.iloc[-1] - sma_50.iloc[-1]) / sma_50.iloc[-1]
        ema_trend = (ema_12.iloc[-1] - ema_26.iloc[-1]) / ema_26.iloc[-1]
        
        # Volatility analysis
        returns = close.pct_change().dropna()
        current_vol = returns.tail(20).std()
        historical_vol = returns.std()
        vol_ratio = current_vol / historical_vol if historical_vol > 0 else 1
        
        # Price momentum
        momentum_5 = (close.iloc[-1] - close.iloc[-6]) / close.iloc[-6] if len(close) > 5 else 0
        momentum_20 = (close.iloc[-1] - close.iloc[-21]) / close.iloc[-21] if len(close) > 20 else 0
        
        # Market condition classification
        if vol_ratio > 1.8:
            return 'high_volatility'
        elif abs(sma_trend) > 0.05 and abs(momentum_20) > 0.1:
            return 'strong_trend'
        elif abs(sma_trend) > 0.02 and abs(momentum_5) > 0.03:
            return 'weak_trend'
        elif vol_ratio < 0.7 and abs(momentum_20) < 0.05:
            return 'low_volatility'
        else:
            return 'ranging'
    
    def create_adaptive_ensemble(self, symbol: str, interval: str, start_time: datetime, 
                                end_time: datetime, market_condition: str = None) -> pd.DataFrame:
        """
        Create adaptive ensemble based on market conditions
        """
        print(f"🔄 Creating adaptive ensemble for {symbol} {interval}...")
        
        # Fetch all strategy data
        strategies_data = {}
        for strategy_name, strategy_func in self.strategies.items():
            try:
                df = strategy_func(symbol=symbol, interval=interval, 
                                 start_time=start_time, end_time=end_time)
                strategies_data[strategy_name] = df
                print(f"  ✅ {strategy_name} data fetched")
            except Exception as e:
                print(f"  ❌ {strategy_name} failed: {e}")
                continue
        
        if not strategies_data:
            return pd.DataFrame()
        
        # Determine market condition if not provided
        if market_condition is None:
            sample_df = list(strategies_data.values())[0]
            market_condition = self.detect_market_condition(sample_df)
        
        print(f"  📊 Detected market condition: {market_condition}")
        
        # Adaptive strategy weights based on market condition
        if market_condition == 'strong_trend':
            weights = {
                'ema_crossover': 0.4,
                'supertrend': 0.3,
                'advanced_momentum': 0.3
            }
            threshold = 0.15
        elif market_condition == 'weak_trend':
            weights = {
                'ema_crossover': 0.35,
                'rsi_macd': 0.35,
                'advanced_momentum': 0.3
            }
            threshold = 0.18
        elif market_condition == 'ranging':
            weights = {
                'bollinger': 0.4,
                'rsi_macd': 0.35,
                'stochastic_rsi': 0.25
            }
            threshold = 0.20
        elif market_condition == 'high_volatility':
            weights = {
                'bollinger': 0.5,
                'rsi_macd': 0.3,
                'stochastic_rsi': 0.2
            }
            threshold = 0.25
        elif market_condition == 'low_volatility':
            weights = {
                'ema_crossover': 0.4,
                'advanced_momentum': 0.35,
                'supertrend': 0.25
            }
            threshold = 0.12
        else:  # uncertain
            weights = {
                'bollinger': 0.3,
                'rsi_macd': 0.3,
                'ema_crossover': 0.2,
                'advanced_momentum': 0.2
            }
            threshold = 0.22
        
        print(f"  🎯 Using weights: {weights}")
        print(f"  🎯 Signal threshold: {threshold}")
        
        # Merge strategies
        base_df = None
        for strategy_name, df in strategies_data.items():
            if strategy_name not in weights:
                continue
                
            if 'signal' not in df.columns:
                df['signal'] = 'WAIT'
            if 'signal_strength' not in df.columns:
                df['signal_strength'] = 0.5
            
            cols = ['timestamp', 'close', 'signal', 'signal_strength']
            temp_df = df[cols].copy()
            temp_df = temp_df.rename(columns={
                'signal': f'signal_{strategy_name}',
                'signal_strength': f'strength_{strategy_name}'
            })
            
            if base_df is None:
                base_df = temp_df
            else:
                base_df = base_df.merge(temp_df, on=['timestamp', 'close'], how='outer')
        
        if base_df is None:
            return pd.DataFrame()
        
        # Calculate ensemble score
        base_df['ensemble_score'] = 0.0
        
        for strategy_name, weight in weights.items():
            signal_col = f'signal_{strategy_name}'
            strength_col = f'strength_{strategy_name}'
            
            if signal_col in base_df.columns:
                signal_numeric = base_df[signal_col].map({'BUY': 1, 'SELL': -1, 'WAIT': 0})
                strength = base_df[strength_col].fillna(0.5)
                base_df['ensemble_score'] += signal_numeric * strength * weight
        
        # Apply additional filters
        base_df = self.apply_market_filters(base_df, market_condition)
        
        # Generate final signals
        base_df['signal'] = 'WAIT'
        base_df.loc[base_df['ensemble_score'] > threshold, 'signal'] = 'BUY'
        base_df.loc[base_df['ensemble_score'] < -threshold, 'signal'] = 'SELL'
        
        # Calculate signal strength
        base_df['signal_strength'] = np.abs(base_df['ensemble_score'])
        base_df['signal_strength'] = np.clip(base_df['signal_strength'], 0.1, 1.0)
        
        # Add market condition to dataframe
        base_df['market_condition'] = market_condition
        
        return base_df[['timestamp', 'close', 'signal', 'signal_strength', 'ensemble_score', 'market_condition']]
    
    def apply_market_filters(self, df: pd.DataFrame, market_condition: str) -> pd.DataFrame:
        """
        Apply market-specific filters
        """
        df = df.copy()
        
        # Calculate additional indicators
        close = df['close']
        
        # Price momentum filters
        df['momentum_short'] = close.pct_change(3)
        df['momentum_medium'] = close.pct_change(10)
        
        # Volatility filter
        df['volatility'] = close.rolling(10).std() / close.rolling(10).mean()
        df['vol_percentile'] = df['volatility'].rolling(30).rank(pct=True)
        
        # Apply condition-specific filters
        if market_condition == 'high_volatility':
            # In high volatility, be more conservative
            vol_filter = df['vol_percentile'] < 0.8
            df.loc[~vol_filter, 'ensemble_score'] *= 0.5
            
        elif market_condition == 'strong_trend':
            # In strong trends, require momentum alignment
            momentum_filter = (
                ((df['ensemble_score'] > 0) & (df['momentum_medium'] > -0.01)) |
                ((df['ensemble_score'] < 0) & (df['momentum_medium'] < 0.01)) |
                (df['ensemble_score'] == 0)
            )
            df.loc[~momentum_filter, 'ensemble_score'] *= 0.3
            
        elif market_condition == 'ranging':
            # In ranging markets, avoid extreme momentum
            momentum_filter = (abs(df['momentum_short']) < 0.02)
            df.loc[~momentum_filter, 'ensemble_score'] *= 0.7
        
        return df
    
    def test_enhanced_system(self, symbol: str = 'BTCUSDT', interval: str = '1h', 
                           days_back: int = 90) -> Dict:
        """
        Test the enhanced system
        """
        print(f"🚀 Testing Enhanced Strategy System")
        print("=" * 50)
        print(f"Symbol: {symbol}")
        print(f"Timeframe: {interval}")
        print(f"Period: {days_back} days")
        
        # Date range
        end_date = datetime.now(timezone.utc)
        start_date = end_date - timedelta(days=days_back)
        
        # Generate adaptive signals
        signals_df = self.create_adaptive_ensemble(symbol, interval, start_date, end_date)
        
        if len(signals_df) == 0:
            return {'error': 'No signals generated'}
        
        # Analyze signals
        signal_counts = signals_df['signal'].value_counts()
        condition_counts = signals_df['market_condition'].value_counts()
        
        print(f"\n📊 Signal Analysis:")
        for signal, count in signal_counts.items():
            print(f"  {signal}: {count} ({count/len(signals_df)*100:.1f}%)")
        
        print(f"\n🌊 Market Conditions:")
        for condition, count in condition_counts.items():
            print(f"  {condition}: {count} ({count/len(signals_df)*100:.1f}%)")
        
        # Run backtest
        print(f"\n🔄 Running backtest...")
        backtester = EnhancedBacktester(
            initial_capital=10000,
            maker_fee=0.001,
            taker_fee=0.001,
            slippage_factor=0.0005
        )
        
        results = backtester.backtest_strategy(signals_df, symbol)
        
        return results

def test_multiple_timeframes():
    """
    Test the enhanced system across multiple timeframes
    """
    print("🚀 ENHANCED STRATEGY SYSTEM - MULTI-TIMEFRAME TEST")
    print("=" * 70)
    
    system = EnhancedStrategySystem()
    
    # Test configurations
    test_configs = [
        {'symbol': 'BTCUSDT', 'interval': '1h', 'days': 60},
        {'symbol': 'BTCUSDT', 'interval': '4h', 'days': 120},
        {'symbol': 'BTCUSDT', 'interval': '1d', 'days': 180},
        {'symbol': 'ETHUSDT', 'interval': '1h', 'days': 60},
        {'symbol': 'ETHUSDT', 'interval': '4h', 'days': 120},
        {'symbol': 'ADAUSDT', 'interval': '4h', 'days': 120},
    ]
    
    results = {}
    
    for config in test_configs:
        config_name = f"{config['symbol']}_{config['interval']}_{config['days']}d"
        print(f"\n{'='*20} {config_name} {'='*20}")
        
        try:
            result = system.test_enhanced_system(
                symbol=config['symbol'],
                interval=config['interval'],
                days_back=config['days']
            )
            
            if 'error' not in result:
                results[config_name] = result
                
                # Display key metrics
                print(f"\n📈 Results:")
                print(f"Total Return: {result.get('total_return', 0):.2%}")
                print(f"Win Rate: {result.get('win_rate', 0):.1%}")
                print(f"Max Drawdown: {result.get('max_drawdown', 0):.2%}")
                print(f"Number of Trades: {result.get('num_trades', 0)}")
                print(f"Sharpe Ratio: {result.get('sharpe_ratio', 0):.2f}")
                print(f"Profit Factor: {result.get('profit_factor', 0):.2f}")
                
            else:
                print(f"❌ Error: {result['error']}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    # Find best configuration
    if results:
        print(f"\n" + "="*70)
        print("🏆 SUMMARY OF ALL TESTS")
        print("="*70)
        
        print(f"\n{'Configuration':<25} {'Return':<8} {'Win Rate':<9} {'Trades':<7} {'Sharpe':<7}")
        print("-" * 65)
        
        best_config = None
        best_score = -float('inf')
        
        for config_name, result in results.items():
            total_return = result.get('total_return', 0)
            win_rate = result.get('win_rate', 0)
            num_trades = result.get('num_trades', 0)
            sharpe = result.get('sharpe_ratio', 0)
            
            # Calculate score
            score = total_return * 100 + win_rate * 50 + sharpe * 25
            
            if score > best_score:
                best_score = score
                best_config = config_name
            
            print(f"{config_name:<25} {total_return:7.1%} {win_rate:8.1%} "
                  f"{num_trades:6d} {sharpe:6.2f}")
        
        if best_config and results[best_config].get('total_return', 0) > 0:
            print(f"\n🥇 BEST CONFIGURATION: {best_config}")
            best_result = results[best_config]
            print(f"✅ This configuration is PROFITABLE!")
            print(f"   Return: {best_result.get('total_return', 0):.2%}")
            print(f"   Win Rate: {best_result.get('win_rate', 0):.1%}")
            print(f"   Max Drawdown: {best_result.get('max_drawdown', 0):.2%}")
            print(f"\n🎯 RECOMMENDATION: Use this configuration for live trading!")
            
            # Extract configuration details
            parts = best_config.split('_')
            symbol = parts[0]
            interval = parts[1]
            
            print(f"\n📋 OPTIMAL SETTINGS:")
            print(f"   Symbol: {symbol}")
            print(f"   Timeframe: {interval}")
            print(f"   Strategy: Enhanced Adaptive System")
            
        else:
            print(f"\n⚠️ No profitable configurations found.")
            print(f"Best performing: {best_config}")
            if best_config:
                best_result = results[best_config]
                print(f"Return: {best_result.get('total_return', 0):.2%}")
    
    return results

if __name__ == "__main__":
    results = test_multiple_timeframes()
