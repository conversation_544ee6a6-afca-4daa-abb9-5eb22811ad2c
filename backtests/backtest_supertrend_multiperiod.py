import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from binance.client import Client
from dotenv import load_dotenv

from strategies.supertrend import fetch_supertrend

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")
client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

# Sonuç klasörleri
LOG_DIR = "backtest_results/logs"
PLOT_DIR = "backtest_results/plots"
os.makedirs(LOG_DIR, exist_ok=True)
os.makedirs(PLOT_DIR, exist_ok=True)

def simulate_trades(df, initial_balance=1000, fee_rate=0.001):
    balance = initial_balance
    position = 0
    portfolio = []
    log_lines = []

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and balance > 0:
            position = (balance * (1 - fee_rate)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f}")
        elif signal == "SELL" and position > 0:
            balance = (position * price) * (1 - fee_rate)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f}")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio

    return df, portfolio[-1], max(portfolio), min(portfolio), log_lines

# Test dönemleri
time_ranges = {
    "2022_Q1": (datetime(2022, 1, 1), datetime(2022, 4, 1)),
    "2023_Q2": (datetime(2023, 4, 1), datetime(2023, 7, 1)),
    "2024_Q3": (datetime(2024, 7, 1), datetime(2024, 10, 1)),
}

for label, (start, end) in time_ranges.items():
    print(f"\n🔁 Testing period: {label} ({start.date()} - {end.date()})")

    df = fetch_supertrend(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=start, end_time=end)
    if df.empty:
        print("⚠️ No data fetched!")
        continue

    print(df["signal"].value_counts())
    df, final_value, max_value, min_value, logs = simulate_trades(df)

    # Log dosyası
    log_path = os.path.join(LOG_DIR, f"backtest_log_{label}_supertrend.txt")
    with open(log_path, "w") as f:
        f.write("\n".join(logs))

    # Grafik
    plt.figure(figsize=(14, 6))
    plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.title(f"📈 Supertrend Strategy Backtest: {label}")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(os.path.join(PLOT_DIR, f"portfolio_plot_{label}_supertrend.png"))
    plt.close()

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_value:.2f} | Min: ${min_value:.2f}")
    print(f"📁 Log saved to {log_path} | 📊 Chart saved to portfolio_plot_{label}_supertrend.png")
