import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)


def generate_bollinger_signals(df, window=20, num_std=2):
    """Enhanced Bollinger Bands with mean reversion confirmation and regime filtering"""
    df = df.copy()

    # Calculate Bollinger Bands
    df["rolling_mean"] = df["close"].rolling(window).mean()
    df["rolling_std"] = df["close"].rolling(window).std()
    df["upper_band"] = df["rolling_mean"] + (num_std * df["rolling_std"])
    df["lower_band"] = df["rolling_mean"] - (num_std * df["rolling_std"])

    # Calculate band position and width
    df["bb_position"] = (df["close"] - df["lower_band"]) / (df["upper_band"] - df["lower_band"])
    df["bb_width"] = (df["upper_band"] - df["lower_band"]) / df["rolling_mean"]
    df["bb_width_percentile"] = df["bb_width"].rolling(50).rank(pct=True)

    # Calculate RSI for momentum confirmation
    df["rsi"] = compute_rsi(df["close"], 14)

    # Calculate trend filter
    df["trend_ema"] = df["close"].ewm(span=50, adjust=False).mean()
    df["trend_direction"] = np.where(df["rolling_mean"] > df["trend_ema"], 1,
                                   np.where(df["rolling_mean"] < df["trend_ema"], -1, 0))

    # Previous values for confirmation
    df["prev_close"] = df["close"].shift(1)
    df["prev_bb_position"] = df["bb_position"].shift(1)
    df["prev_rsi"] = df["rsi"].shift(1)

    # Initialize signals
    df["signal"] = "WAIT"

    # Enhanced BUY conditions (mean reversion from lower band)
    buy_conditions = (
        # Price near or below lower band
        (df["bb_position"] < 0.2) &
        # Price starting to move back toward mean
        (df["close"] > df["prev_close"]) &
        # RSI oversold but starting to recover
        (df["rsi"] < 40) & (df["rsi"] > df["prev_rsi"]) &
        # Bands are not extremely narrow (avoid low volatility periods)
        (df["bb_width_percentile"] > 0.3) &
        # Not in strong downtrend
        (df["trend_direction"] >= -0.5)
    )

    # Enhanced SELL conditions (mean reversion from upper band)
    sell_conditions = (
        # Price near or above upper band
        (df["bb_position"] > 0.8) &
        # Price starting to move back toward mean
        (df["close"] < df["prev_close"]) &
        # RSI overbought but starting to decline
        (df["rsi"] > 60) & (df["rsi"] < df["prev_rsi"]) &
        # Bands are not extremely narrow
        (df["bb_width_percentile"] > 0.3) &
        # Not in strong uptrend
        (df["trend_direction"] <= 0.5)
    )

    df.loc[buy_conditions, "signal"] = "BUY"
    df.loc[sell_conditions, "signal"] = "SELL"

    # Add signal strength scoring
    df["signal_strength"] = 0.0

    # Band position component (0-0.4)
    position_strength = np.where(df["bb_position"] < 0.2, (0.2 - df["bb_position"]) * 2,
                                np.where(df["bb_position"] > 0.8, (df["bb_position"] - 0.8) * 2, 0)) * 0.4

    # RSI divergence component (0-0.3)
    rsi_strength = np.where(df["rsi"] < 30, (30 - df["rsi"]) / 30,
                           np.where(df["rsi"] > 70, (df["rsi"] - 70) / 30, 0)) * 0.3

    # Volatility component (0-0.3)
    volatility_strength = np.clip(df["bb_width_percentile"], 0.3, 1.0) * 0.3

    df["signal_strength"] = position_strength + rsi_strength + volatility_strength
    df["score_bollinger"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})

    return df

def compute_rsi(series, period=14):
    """Calculate RSI indicator"""
    delta = series.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

# shortcut
def fetch_bollinger(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=None, start_time=None, end_time=None):
    if start_time is not None and end_time is not None:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    elif lookback_days is not None:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=lookback_days)
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    else:
        raise ValueError("lookback_days veya start_time ve end_time verilmelidir.")


    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = df[col].astype(float)
        
        # 3) keep OHLC + volume (so regime code sees high/low/close)
        df_list.append(df[["timestamp", "open", "high", "low", "close", "volume"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    df = pd.concat(df_list, ignore_index=True)
    df = generate_bollinger_signals(df)
    return df


# Kullanım örneği
if __name__ == "__main__":
    df = fetch_bollinger()
    df = generate_bollinger_signals(df)
    print(df[["timestamp", "close", "middle_band", "upper_band", "lower_band", "signal"]].tail(20))
