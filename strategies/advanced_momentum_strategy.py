#!/usr/bin/env python3
"""
Advanced Momentum Strategy for Crypto Trading
Combines multiple momentum indicators with market regime detection
"""

import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta, timezone

# Load environment variables
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_advanced_momentum(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, 
                           lookback_days=None, start_time=None, end_time=None):
    """Fetch data and generate advanced momentum signals"""
    
    if start_time and end_time:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    elif lookback_days:
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(days=lookback_days)
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    else:
        raise ValueError("Either lookback_days or start_time and end_time must be provided.")

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        
        # Cast all OHLC + volume
        for col in ["open", "high", "low", "close", "volume"]:
            df[col] = df[col].astype(float)

        df_list.append(df[["timestamp", "open", "high", "low", "close", "volume"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    df = pd.concat(df_list, ignore_index=True)
    df = generate_advanced_momentum_signals(df)
    return df

def generate_advanced_momentum_signals(df):
    """Generate advanced momentum signals with multiple confirmations"""
    df = df.copy()
    
    # === MOMENTUM INDICATORS ===
    
    # 1. Multi-timeframe RSI
    df["rsi_14"] = compute_rsi(df["close"], 14)
    df["rsi_21"] = compute_rsi(df["close"], 21)
    df["rsi_avg"] = (df["rsi_14"] + df["rsi_21"]) / 2
    
    # 2. MACD with histogram
    df["macd"], df["macd_signal"] = compute_macd(df["close"])
    df["macd_histogram"] = df["macd"] - df["macd_signal"]
    
    # 3. Stochastic oscillator
    df["stoch_k"], df["stoch_d"] = compute_stochastic(df["high"], df["low"], df["close"])
    
    # 4. Rate of Change (ROC)
    df["roc_10"] = df["close"].pct_change(10) * 100
    df["roc_20"] = df["close"].pct_change(20) * 100
    
    # === TREND AND VOLATILITY FILTERS ===
    
    # 5. Multiple EMAs for trend
    df["ema_8"] = df["close"].ewm(span=8, adjust=False).mean()
    df["ema_21"] = df["close"].ewm(span=21, adjust=False).mean()
    df["ema_50"] = df["close"].ewm(span=50, adjust=False).mean()
    
    # 6. ATR and volatility
    df["atr"] = compute_atr(df["high"], df["low"], df["close"])
    df["volatility_rank"] = df["atr"].rolling(50).rank(pct=True)
    
    # 7. Volume analysis
    df["volume_sma"] = df["volume"].rolling(20).mean()
    df["volume_ratio"] = df["volume"] / df["volume_sma"]
    
    # === MARKET REGIME DETECTION ===
    
    # Trend strength
    df["trend_strength"] = np.abs(df["ema_8"] - df["ema_50"]) / df["ema_50"] * 100
    df["trend_direction"] = np.where(df["ema_8"] > df["ema_21"], 1,
                                   np.where(df["ema_8"] < df["ema_21"], -1, 0))
    
    # Market regime classification
    df["regime"] = "RANGING"
    df.loc[(df["trend_strength"] > 2) & (df["volatility_rank"] < 0.7), "regime"] = "TRENDING"
    df.loc[df["volatility_rank"] > 0.8, "regime"] = "VOLATILE"
    
    # === SIGNAL GENERATION ===
    
    # Previous values for crossover detection
    df["prev_macd"] = df["macd"].shift(1)
    df["prev_macd_signal"] = df["macd_signal"].shift(1)
    df["prev_stoch_k"] = df["stoch_k"].shift(1)
    df["prev_stoch_d"] = df["stoch_d"].shift(1)
    
    # Initialize signals
    df["signal"] = "WAIT"
    
    # === BULLISH CONDITIONS ===
    bullish_momentum = (
        # RSI conditions
        (df["rsi_avg"] < 50) & (df["rsi_14"] > df["rsi_14"].shift(1)) &
        # MACD bullish crossover
        (df["macd"] > df["macd_signal"]) & (df["prev_macd"] <= df["prev_macd_signal"]) &
        # MACD histogram increasing
        (df["macd_histogram"] > df["macd_histogram"].shift(1)) &
        # Stochastic confirmation
        (df["stoch_k"] > df["stoch_d"]) & (df["prev_stoch_k"] <= df["prev_stoch_d"]) &
        # ROC momentum
        (df["roc_10"] > 0) &
        # Trend alignment
        (df["trend_direction"] >= 0) &
        # Volume confirmation
        (df["volume_ratio"] > 1.1) &
        # Volatility filter
        (df["volatility_rank"] < 0.9)
    )
    
    # === BEARISH CONDITIONS ===
    bearish_momentum = (
        # RSI conditions
        (df["rsi_avg"] > 50) & (df["rsi_14"] < df["rsi_14"].shift(1)) &
        # MACD bearish crossover
        (df["macd"] < df["macd_signal"]) & (df["prev_macd"] >= df["prev_macd_signal"]) &
        # MACD histogram decreasing
        (df["macd_histogram"] < df["macd_histogram"].shift(1)) &
        # Stochastic confirmation
        (df["stoch_k"] < df["stoch_d"]) & (df["prev_stoch_k"] >= df["prev_stoch_d"]) &
        # ROC momentum
        (df["roc_10"] < 0) &
        # Trend alignment
        (df["trend_direction"] <= 0) &
        # Volume confirmation
        (df["volume_ratio"] > 1.1) &
        # Volatility filter
        (df["volatility_rank"] < 0.9)
    )
    
    # Apply regime-specific filters
    trending_filter = df["regime"] == "TRENDING"
    ranging_filter = df["regime"] == "RANGING"
    
    # Only trade in favorable regimes
    df.loc[bullish_momentum & (trending_filter | ranging_filter), "signal"] = "BUY"
    df.loc[bearish_momentum & (trending_filter | ranging_filter), "signal"] = "SELL"
    
    # === SIGNAL STRENGTH CALCULATION ===
    df["signal_strength"] = calculate_signal_strength(df)
    df["score_advanced_momentum"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})
    
    return df

def calculate_signal_strength(df):
    """Calculate signal strength based on multiple factors"""
    strength = np.zeros(len(df))
    
    # RSI component (0-0.25)
    rsi_component = np.where(df["rsi_avg"] < 30, (30 - df["rsi_avg"]) / 30,
                            np.where(df["rsi_avg"] > 70, (df["rsi_avg"] - 70) / 30, 0)) * 0.25
    
    # MACD component (0-0.25)
    macd_component = np.abs(df["macd_histogram"]) / df["atr"] * 0.25
    macd_component = np.clip(macd_component, 0, 0.25)
    
    # Volume component (0-0.2)
    volume_component = np.clip((df["volume_ratio"] - 1) * 0.2, 0, 0.2)
    
    # Trend alignment component (0-0.15)
    trend_component = np.where(
        (df["signal"] == "BUY") & (df["trend_direction"] > 0), 0.15,
        np.where((df["signal"] == "SELL") & (df["trend_direction"] < 0), 0.15, 0)
    )
    
    # ROC momentum component (0-0.15)
    roc_component = np.clip(np.abs(df["roc_10"]) / 5, 0, 0.15)
    
    return rsi_component + macd_component + volume_component + trend_component + roc_component

# === HELPER FUNCTIONS ===

def compute_rsi(series, period=14):
    """Calculate RSI indicator"""
    delta = series.diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)
    avg_gain = gain.rolling(window=period).mean()
    avg_loss = loss.rolling(window=period).mean()
    rs = avg_gain / avg_loss
    return 100 - (100 / (1 + rs))

def compute_macd(series, fast=12, slow=26, signal=9):
    """Calculate MACD indicator"""
    ema_fast = series.ewm(span=fast, adjust=False).mean()
    ema_slow = series.ewm(span=slow, adjust=False).mean()
    macd = ema_fast - ema_slow
    macd_signal = macd.ewm(span=signal, adjust=False).mean()
    return macd, macd_signal

def compute_stochastic(high, low, close, k_period=14, d_period=3):
    """Calculate Stochastic oscillator"""
    lowest_low = low.rolling(window=k_period).min()
    highest_high = high.rolling(window=k_period).max()
    k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
    d_percent = k_percent.rolling(window=d_period).mean()
    return k_percent, d_percent

def compute_atr(high, low, close, period=14):
    """Calculate Average True Range"""
    tr1 = high - low
    tr2 = np.abs(high - close.shift(1))
    tr3 = np.abs(low - close.shift(1))
    true_range = np.maximum(tr1, np.maximum(tr2, tr3))
    return true_range.rolling(window=period).mean()

if __name__ == "__main__":
    # Test the strategy
    df = fetch_advanced_momentum(lookback_days=30)
    print("Advanced Momentum Strategy Test:")
    print(df[["timestamp", "close", "signal", "signal_strength", "regime"]].tail(10))
    
    # Print signal distribution
    signal_counts = df["signal"].value_counts()
    print(f"\nSignal Distribution:")
    for signal, count in signal_counts.items():
        print(f"{signal}: {count} ({count/len(df)*100:.1f}%)")
