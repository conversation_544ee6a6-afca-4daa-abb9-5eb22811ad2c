import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime
from pathlib import Path

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_ohlcv_range(symbol, interval, start_time, end_time):
    df_list = []
    current_start = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )
        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)

        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def calculate_rsi(prices, period=14):
    delta = prices.diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)
    avg_gain = pd.Series(gain).rolling(window=period).mean()
    avg_loss = pd.Series(loss).rolling(window=period).mean()
    rs = avg_gain / (avg_loss + 1e-9)
    rsi = 100 - (100 / (1 + rs))
    return rsi

def calculate_macd(prices, fast=12, slow=26, signal=9):
    ema_fast = prices.ewm(span=fast).mean()
    ema_slow = prices.ewm(span=slow).mean()
    macd = ema_fast - ema_slow
    signal_line = macd.ewm(span=signal).mean()
    histogram = macd - signal_line
    return macd, signal_line, histogram

def generate_signals(df):
    df["rsi"] = calculate_rsi(df["close"])
    df["macd"], df["macd_signal"], df["macd_hist"] = calculate_macd(df["close"])
    df["signal"] = "WAIT"
    df.loc[(df["rsi"] < 30) & (df["macd"] > df["macd_signal"]) & (df["macd_hist"] > 0), "signal"] = "BUY"
    df.loc[(df["rsi"] > 70) & (df["macd"] < df["macd_signal"]) & (df["macd_hist"] < 0), "signal"] = "SELL"
    return df

def simulate_trades(df, initial_balance=1000, fee_rate=0.001):
    balance = initial_balance
    position = 0
    portfolio = []
    log_lines = []

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and balance > 0:
            position = (balance * (1 - fee_rate)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f}")
        elif signal == "SELL" and position > 0:
            balance = (position * price) * (1 - fee_rate)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f}")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio
    return df, portfolio[-1], max(portfolio), min(portfolio), log_lines

# Test edilecek zaman aralıkları
time_ranges = {
    "2022_Q1": (datetime(2022, 1, 1), datetime(2022, 4, 1)),
    "2023_Q2": (datetime(2023, 4, 1), datetime(2023, 7, 1)),
    "2024_Q3": (datetime(2024, 7, 1), datetime(2024, 10, 1)),
}

# Sonuç klasörlerini oluştur
results_dir = Path("backtest_results")
log_dir = results_dir / "logs"
chart_dir = results_dir / "plots"
log_dir.mkdir(parents=True, exist_ok=True)
chart_dir.mkdir(parents=True, exist_ok=True)

# .gitignore'a ekle
gitignore_path = Path(".gitignore")
ignore_entries = ["backtest_results/logs/", "backtest_results/plots/"]
if gitignore_path.exists():
    existing_lines = gitignore_path.read_text().splitlines()
else:
    existing_lines = []

with open(gitignore_path, "a") as f:
    for entry in ignore_entries:
        if entry not in existing_lines:
            f.write(f"{entry}\n")

# Backtest döngüsü
for label, (start, end) in time_ranges.items():
    print(f"\n🔁 Testing period: {label} ({start.date()} - {end.date()})")

    df = fetch_ohlcv_range("BTCUSDT", Client.KLINE_INTERVAL_15MINUTE, start, end)
    df = generate_signals(df)
    df, final_value, max_value, min_value, logs = simulate_trades(df)

    # Log dosyasını kaydet
    log_filename = log_dir / f"backtest_log_{label}_rsimacd.txt"
    with open(log_filename, "w") as f:
        f.write("\n".join(logs))

    # Grafik kaydet
    plt.figure(figsize=(14, 6))
    plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.title(f"📈 RSI + MACD Backtest: {label}")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig(chart_dir / f"portfolio_plot_{label}_rsimacd.png")
    plt.close()

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_value:.2f} | Min: ${min_value:.2f}")
    print(f"📁 Log saved to {log_filename} | 📊 Chart saved to {chart_dir / f'portfolio_plot_{label}.png'}")
