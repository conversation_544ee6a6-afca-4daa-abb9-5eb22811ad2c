import pandas as pd
import numpy as np
import os
from binance.client import Client
from dotenv import load_dotenv
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_ema_crossover(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=None, start_time=None, end_time=None):
    if start_time and end_time:
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    elif lookback_days:
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=lookback_days)
        start_ts = int(start_time.timestamp() * 1000)
        end_ts = int(end_time.timestamp() * 1000)
    else:
        raise ValueError("lookback_days veya start_time ve end_time verilmelidir.")

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        # cast all OHLC + volume
        for col in ["open","high","low","close","volume"]:
            df[col] = df[col].astype(float)
    
        # keep timestamp + full OHLC/volume so regime detector works
        df_list.append(df[["timestamp","open","high","low","close","volume"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    df = pd.concat(df_list, ignore_index=True)
    df = generate_ema_crossover_signals(df)
    return df


def generate_ema_crossover_signals(df, short_window=12, long_window=26):
    """Enhanced EMA crossover with proper crossover detection and filtering"""
    df = df.copy()

    # Calculate EMAs
    df["ema_short"] = df["close"].ewm(span=short_window, adjust=False).mean()
    df["ema_long"] = df["close"].ewm(span=long_window, adjust=False).mean()

    # Calculate trend strength using ADX-like measure
    df["ema_diff"] = df["ema_short"] - df["ema_long"]
    df["ema_diff_pct"] = df["ema_diff"] / df["ema_long"] * 100

    # Calculate volatility and trend strength filters
    df["atr"] = df["close"].rolling(14).apply(lambda x: np.mean(np.abs(x.diff())))
    df["trend_strength"] = np.abs(df["ema_diff_pct"])
    df["volatility_rank"] = df["atr"].rolling(50).rank(pct=True)

    # Previous values for crossover detection
    df["prev_ema_short"] = df["ema_short"].shift(1)
    df["prev_ema_long"] = df["ema_long"].shift(1)
    df["prev_ema_diff"] = df["ema_diff"].shift(1)

    # Volume filter (if available)
    volume_filter = True
    if "volume" in df.columns:
        df["volume_ma"] = df["volume"].rolling(20).mean()
        df["volume_ratio"] = df["volume"] / df["volume_ma"]
        volume_filter = df["volume_ratio"] > 1.2  # Above average volume

    # Initialize signals
    df["signal"] = "WAIT"

    # Enhanced BUY conditions (bullish crossover)
    buy_conditions = (
        # Bullish crossover: short EMA crosses above long EMA
        (df["ema_short"] > df["ema_long"]) &
        (df["prev_ema_short"] <= df["prev_ema_long"]) &
        # Momentum confirmation: difference is increasing
        (df["ema_diff"] > df["prev_ema_diff"]) &
        # Trend strength filter: avoid weak trends
        (df["trend_strength"] > 0.1) &
        # Volatility filter: avoid extremely volatile periods
        (df["volatility_rank"] < 0.9) &
        # Volume confirmation (if available)
        volume_filter
    )

    # Enhanced SELL conditions (bearish crossover)
    sell_conditions = (
        # Bearish crossover: short EMA crosses below long EMA
        (df["ema_short"] < df["ema_long"]) &
        (df["prev_ema_short"] >= df["prev_ema_long"]) &
        # Momentum confirmation: difference is decreasing
        (df["ema_diff"] < df["prev_ema_diff"]) &
        # Trend strength filter: avoid weak trends
        (df["trend_strength"] > 0.1) &
        # Volatility filter: avoid extremely volatile periods
        (df["volatility_rank"] < 0.9) &
        # Volume confirmation (if available)
        volume_filter
    )

    df.loc[buy_conditions, "signal"] = "BUY"
    df.loc[sell_conditions, "signal"] = "SELL"

    # Add signal strength scoring
    df["signal_strength"] = 0.0

    # Trend strength component (0-0.5)
    trend_component = np.clip(df["trend_strength"] / 2.0, 0, 0.5)

    # Momentum component (0-0.3)
    momentum_component = np.clip(np.abs(df["ema_diff"] - df["prev_ema_diff"]) / df["atr"] * 0.3, 0, 0.3)

    # Volume component (0-0.2)
    if "volume" in df.columns:
        volume_component = np.clip((df["volume_ratio"] - 1) * 0.2, 0, 0.2)
    else:
        volume_component = 0.1  # Default moderate strength

    df["signal_strength"] = trend_component + momentum_component + volume_component
    df["score_ema_crossover"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})

    return df
