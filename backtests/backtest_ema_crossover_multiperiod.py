import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

# Klasörler
PLOT_DIR = "backtest_results/plots"
LOG_DIR = "backtest_results/logs"
os.makedirs(PLOT_DIR, exist_ok=True)
os.makedirs(LOG_DIR, exist_ok=True)

def fetch_ohlcv_range(symbol, interval, start_time, end_time):
    df_list = []
    current_start = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )
        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)

        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def generate_ema_signals(df, short_window=12, long_window=26):
    df["ema_short"] = df["close"].ewm(span=short_window, adjust=False).mean()
    df["ema_long"] = df["close"].ewm(span=long_window, adjust=False).mean()

    df["signal"] = "WAIT"
    df.loc[df["ema_short"] > df["ema_long"], "signal"] = "BUY"
    df.loc[df["ema_short"] < df["ema_long"], "signal"] = "SELL"
    return df

def simulate_trades(df, initial_balance=1000, fee_rate=0.001):
    balance = initial_balance
    position = 0
    portfolio = []
    log_lines = []

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and balance > 0:
            position = (balance * (1 - fee_rate)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f}")
        elif signal == "SELL" and position > 0:
            balance = (position * price) * (1 - fee_rate)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f}")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio
    return df, portfolio[-1], max(portfolio), min(portfolio), log_lines

# Test zaman dilimleri (3 aylık periyotlar)
time_ranges = {
    "2022_Q1": (datetime(2022, 1, 1), datetime(2022, 4, 1)),
    "2023_Q2": (datetime(2023, 4, 1), datetime(2023, 7, 1)),
    "2024_Q3": (datetime(2024, 7, 1), datetime(2024, 10, 1)),
}

for label, (start, end) in time_ranges.items():
    print(f"\n🔁 Testing period: {label} ({start.date()} - {end.date()})")

    df = fetch_ohlcv_range("BTCUSDT", Client.KLINE_INTERVAL_15MINUTE, start, end)
    df = generate_ema_signals(df)
    df, final_value, max_value, min_value, logs = simulate_trades(df)

    # Log dosyası
    log_filename = os.path.join(LOG_DIR, f"backtest_log_{label}_ema.txt")
    with open(log_filename, "w") as f:
        f.write("\n".join(logs))

    # Grafik dosyası
    plt.figure(figsize=(14, 6))
    plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.title(f"📈 EMA Crossover Backtest: {label}")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plot_filename = os.path.join(PLOT_DIR, f"portfolio_plot_{label}_ema.png")
    plt.savefig(plot_filename)
    plt.close()

    print(f"💼 Final Value: ${final_value:.2f} | Max: ${max_value:.2f} | Min: ${min_value:.2f}")
    print(f"📁 Log saved to {log_filename} | 📊 Chart saved to {plot_filename}")
