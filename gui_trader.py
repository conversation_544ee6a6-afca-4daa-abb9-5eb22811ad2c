# gui_trader.py
import threading
import time
from datetime import datetime, timedelta

import Py<PERSON><PERSON><PERSON><PERSON><PERSON> as sg
from binance.client import Client

from utils.telegram_notify import send_telegram_message
from strategies.UPDATED_bollinger    import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd      import fetch_rsi_macd
from strategies.supertrend           import fetch_supertrend
from strategies.stochastic_rsi       import fetch_stoch_rsi
info_window = None

# ── GLOBAL STYLE SETTINGS ─────────────────────────────────────────────────────
sg.theme('DarkTeal9')                # or 'DarkBlue3', 'SystemDefault' etc.
sg.set_options(
    font=('Helvetica', 13),          # base font & size
    element_padding=(8, 6),          # a little more space around each element
    button_color=('white','darkgreen')  # default button colors
)

# ── Defaults ──────────────────────────────────────────────────────────────────
DEFAULTS = {
    "poll_interval":    300,      # seconds
    "lookback_days":    1,
    "initial_balance":  10_000.0, # USD
    "risk_per_trade":   0.01,     # 1% per trade
    "stop_loss_pct":    0.02,     # 2% SL
    "take_profit_pct":  0.04,     # 4% TP
    "fee_rate":         0.001,    # 0.1% fee
    "slippage_rate":    0.0005,   # 0.05% slippage
    "signal_threshold": 0.2,
    "weights": {
        "bollinger":      0.25,
        "ema_crossover":  0.15,
        "rsi_macd":       0.25,
        "stochastic_rsi": 0.20,
        "supertrend":     0.15,
    }
}

SIGNAL_SCORE = {"BUY": 1, "WAIT": 0, "SELL": -1}


# ── Helpers ───────────────────────────────────────────────────────────────────
def get_btc_price():
    c = Client()  # BINANCE creds from env
    return float(c.get_symbol_ticker(symbol="BTCUSDT")["price"])


def fetch_live_signals(params):
    """Grab the last N days of data and return each strategy's DataFrame."""
    now  = datetime.utcnow()
    past = now - timedelta(days=params["lookback_days"])
    return {
      "bollinger":      fetch_bollinger(   symbol="BTCUSDT", interval="15m",
                                           start_time=past, end_time=now),
      "ema_crossover":  fetch_ema_crossover(symbol="BTCUSDT", interval="15m",
                                           start_time=past, end_time=now),
      "rsi_macd":       fetch_rsi_macd(     symbol="BTCUSDT", interval="15m",
                                           start_time=past, end_time=now),
      "stochastic_rsi": fetch_stoch_rsi(    symbol="BTCUSDT", interval="15m",
                                           start_time=past, end_time=now),
      "supertrend":     fetch_supertrend(   symbol="BTCUSDT", interval="15m",
                                           start_time=past, end_time=now),
    }


def merge_signals(signals, params):
    """Build one DataFrame of timestamp + each signal + each score + weighted_score + final_signal."""
    df = None
    for name, strat in signals.items():
        s = strat[["timestamp", "signal"]].copy().rename(
            columns={"signal": f"signal_{name}"})
        s[f"score_{name}"] = s[f"signal_{name}"].map(SIGNAL_SCORE)
        df = s if df is None else df.merge(s, on="timestamp", how="outer")

    df.sort_values("timestamp", inplace=True)
    df.ffill().bfill(inplace=True)

    # Guarantee every weight has a score column
    for name in params["weights"]:
        col = f"score_{name}"
        if col not in df.columns:
            df[col] = 0

    # Weighted sum
    df["weighted_score"] = sum(
        df[f"score_{n}"] * w
        for n, w in params["weights"].items()
    )
    # Final
    thr = params["signal_threshold"]
    df["final_signal"] = df["weighted_score"].apply(
        lambda x: "BUY"  if x >  thr
                  else "SELL" if x < -thr
                  else "WAIT"
    )
    return df


# ── Trading / UI Thread ───────────────────────────────────────────────────────
def trading_loop(window, params):
    balance   = params["initial_balance"]
    position  = None        # either "LONG" or None
    quantity  = 0.0
    entry     = 0.0
    sl        = 0.0
    tp        = 0.0

    while getattr(window, "running", False):
        # 1) fetch/merge signals + price
        sigs   = fetch_live_signals(params)
        df     = merge_signals(sigs, params)
        latest = df.iloc[-1]
        price  = get_btc_price()
        now    = datetime.utcnow().strftime("%Y-%m-%d %H:%M:%S")

        # 2) maybe OPEN a new long
        if position is None and latest["final_signal"] == "BUY":
            risk_amount   = balance * params["risk_per_trade"]
            entry         = price * (1 + params["slippage_rate"])
            sl            = entry * (1 - params["stop_loss_pct"])
            tp            = entry * (1 + params["take_profit_pct"])
            quantity      = risk_amount / (entry - sl)
            cost          = quantity * entry
            fee           = cost * params["fee_rate"]
            total_spent   = cost + fee
            if total_spent <= balance:
                balance  -= total_spent
                position  = "LONG"
                send_telegram_message(
                    f"🟢 OPEN LONG {quantity:.6f} BTC @ ${entry:.2f}\n"
                    f"   SL @ ${sl:.2f}  TP @ ${tp:.2f}\n"
                    f"   Risk ${risk_amount:.2f}, Fee ${fee:.2f}"
                )

        # 3) maybe CLOSE it
        elif position == "LONG":
            # on SELL signal or SL/TP hit
            if (latest["final_signal"] == "SELL") or (price <= sl) or (price >= tp):
                exit_price = price * (1 - params["slippage_rate"])
                proceeds    = quantity * exit_price
                fee_close   = proceeds * params["fee_rate"]
                pnl         = proceeds - fee_close - (quantity * entry)
                balance    += (proceeds - fee_close)
                send_telegram_message(
                    f"🛑 CLOSE LONG @ ${exit_price:.2f} | P&L ${pnl:.2f}\n"
                    f"   Equity ${balance:.2f}"
                )
                position = None

        # 4) update UI & Telegram heartbeat
        update = {
            "time":     now,
            "price":    price,
            "signal":   latest["final_signal"],
            "score":    latest["weighted_score"],
            "balance":  balance,
            "position": position or "NONE"
        }
        # Push to the GUI event loop
        window.write_event_value("-UPDATE-", update)

        # ——— Telegram Heartbeat —————————————————————————————————————————————
        heartbeat = [
            f"🕒 {now}",
            f"💰 Balance: ${balance:.2f}",
            f"📈 BTC/USDT: ${price:.2f}",
            "🔔 Signals:",
            f"   Bollinger:      {latest['signal_bollinger']}",
            f"   EMA Crossover:  {latest['signal_ema_crossover']}",
            f"   RSI+MACD:       {latest['signal_rsi_macd']}",
            f"   Stoch RSI:      {latest['signal_stochastic_rsi']}",
            f"   Supertrend:     {latest['signal_supertrend']}",
            f"📊 Score:         {latest['weighted_score']:.2f}",
            f"▶️ Position:      {position or 'NONE'}",
        ]
        send_telegram_message("\n".join(heartbeat))

        # 5) wait for next poll
        time.sleep(params["poll_interval"])



# ── Build the GUI ─────────────────────────────────────────────────────────────
def build_window():
    ws = DEFAULTS["weights"]

    # Labels for the inputs
    label_font = ('Helvetica', 14, 'bold')
    input_size = (8,1)
    header = [[ sg.Text("⏱ AlgoTrader Dashboard", font=('Helvetica', 20, 'bold'), justification='center', expand_x=True) ]]

    weight_layout = [
        [ sg.Text("Strategy Weights (sum=1.0)", font=label_font) ],
        [
            sg.Text("Bollinger", size=(10,1)), sg.Input(f"{ws['bollinger']:.2f}", key="-W-BB-", size=input_size),
            sg.Text("EMA Xover", size=(10,1)), sg.Input(f"{ws['ema_crossover']:.2f}", key="-W-EMA-", size=input_size),
            sg.Text("RSI+MACD", size=(10,1)), sg.Input(f"{ws['rsi_macd']:.2f}", key="-W-RSI-", size=input_size),
        ],
        [
            sg.Text("StochRSI", size=(10,1)), sg.Input(f"{ws['stochastic_rsi']:.2f}", key="-W-SR-", size=input_size),
            sg.Text("Supertrend", size=(10,1)), sg.Input(f"{ws['supertrend']:.2f}", key="-W-ST-", size=input_size),
        ]
    ]

    risk_layout = [
        [ sg.Text("Risk/SL/TP (in %)", font=label_font) ],
        [
            sg.Text("Risk", size=(8,1)),       sg.Input(f"{DEFAULTS['risk_per_trade']*100:.1f}", key="-RISK-", size=input_size),
            sg.Text("SL", size=(8,1)),         sg.Input(f"{DEFAULTS['stop_loss_pct']*100:.1f}", key="-SL-", size=input_size),
            sg.Text("TP", size=(8,1)),         sg.Input(f"{DEFAULTS['take_profit_pct']*100:.1f}", key="-TP-", size=input_size),
        ]
    ]

    poll_layout = [
        sg.Text("Poll Interval (s):", font=label_font), 
        sg.Input(DEFAULTS["poll_interval"], key="-POLL-", size=(6,1))
    ]

    btn_layout = [
        sg.Button("Info",  key="Info",  button_color=('white','royalblue')),
        sg.Button("Start", key="Start", button_color=('white','green')),
        sg.Button("Stop",  key="Stop",  button_color=('white','firebrick')),
        sg.Button("Apply", key="Apply", button_color=('white','darkorange')),
        sg.Exit("Exit",   key="Exit",   button_color=('white','grey'))
    ]

    status_frame = sg.Frame("Status", [
        [ sg.Text("Time:", font=label_font),     sg.Text("", key="-TIME-") ],
        [ sg.Text("Balance:", font=label_font),  sg.Text("", key="-BAL-") ],
        [ sg.Text("Price:", font=label_font),    sg.Text("", key="-PRI-") ],
        [ sg.Text("Signal:", font=label_font),   sg.Text("", key="-SIG-") ],
        [ sg.Text("Score:", font=label_font),    sg.Text("", key="-SCO-") ],
        [ sg.Text("Position:", font=label_font), sg.Text("", key="-POS-") ],
    ], title_location='n', relief=sg.RELIEF_SUNKEN, pad=((0,0),(10,10)))

    timer_row = [ 
        sg.Text("⏳ Next update in:", font=label_font),
        sg.Text("0s", key="-TIMER-", font=label_font),
        sg.Text("", key="-SPINNER-", font=('Helvetica',18,'bold'))
    ]

    layout = (
        header
        + weight_layout
        + risk_layout
        + [poll_layout]
        + [btn_layout]
        + [[status_frame]]
        + [timer_row]
    )

    return sg.Window(
        "AlgoTrader Desktop UI", 
        layout,
        finalize=True,
        resizable=True,
        background_color=sg.theme_background_color()
    )


# ── Main ───────────────────────────────────────────────────────────────────────
if __name__ == "__main__":
    window = build_window()
    window.running = False
    params = DEFAULTS.copy()

    next_update = 0
    spinner_frames = ['◐','◓','◑','◒']
    spinner_i = 0
    # Event loop
    while True:
        event, values = window.read(timeout=250)
        
        if event in (sg.WIN_CLOSED, "Exit"):
            window.running = False
            break
        
        if window.running:
            # advance spinner
            spinner_i = (spinner_i + 1) % len(spinner_frames)
            window['-SPINNER-'].update(spinner_frames[spinner_i])

            # update countdown once per second
            if next_update_time is not None:
                secs_left = int(max(0, next_update_time - time.time()))
                window['-TIMER-'].update(f"{secs_left}s")
            else:
                # static when stopped
                window['-SPINNER-'].update(spinner_frames[0])
                window['-TIMER-'].update("0s")

        # Apply new parameters
        if event == "Info" and info_window is None:
            # Build a small, modal info window in the main thread
            info_layout = [
                [sg.Text(
                    "Parameter Guide\n\n"
                    "• Risk %: Fraction of your account equity you’re willing to risk per trade\n"
                    "  (e.g. 1% means that if the stop‑loss is hit, you lose at most 1% of your balance).\n\n"
                    "• SL % (Stop‑Loss): How far below the entry price to place your stop‑loss\n"
                    "  (e.g. 2% below entry).\n\n"
                    "• TP % (Take‑Profit): How far above the entry price to take profit\n"
                    "  (e.g. 4% above entry).\n\n"
                    "• Fee Rate: Exchange commission per trade\n"
                    "  (e.g. 0.1% = 0.001).\n\n"
                    "• Slippage Rate: Expected adverse price movement during execution\n"
                    "  (e.g. 0.05% = 0.0005).\n\n"
                    "👉 Adjust these parameters in the main UI and click Apply to make changes live.",
                    size=(60, 20), pad=(10,10)
                )],
                [sg.Button("Close", size=(10,1))]
            ]
            info_window = sg.Window(
                "AlgoTrader Parameters Info",
                info_layout,
                modal=True,
                keep_on_top=True,
                finalize=True
            )

        # And *also* right after that, still inside your main loop, service that info window:
        if info_window:
            ev2, vals2 = info_window.read(timeout=0)
            if ev2 in (sg.WIN_CLOSED, "Close"):
                info_window.close()
                info_window = None

        if event == "Apply":
            try:
                new_w = {
                    "bollinger":      float(values["-W-BB-"]),
                    "ema_crossover":  float(values["-W-EMA-"]),
                    "rsi_macd":       float(values["-W-RSI-"]),
                    "stochastic_rsi": float(values["-W-SR-"]),
                    "supertrend":     float(values["-W-ST-"]),
                }
                if abs(sum(new_w.values()) - 1.0) > 1e-3:
                    raise ValueError("Weights must sum to 1.0")
                params["weights"]         = new_w
                params["risk_per_trade"]  = float(values["-RISK-"])/100
                params["stop_loss_pct"]   = float(values["-SL-"])/100
                params["take_profit_pct"] = float(values["-TP-"])/100
                params["poll_interval"]   = int(values["-POLL-"])
                sg.popup_ok("✅ Parameters updated!")
            except Exception as e:
                sg.popup_error(f"Invalid input: {e}")

        # Start / Stop the background thread
        if event == "Start" and not window.running:
            window.running = True
            next_update_time = time.time() + params["poll_interval"]
            threading.Thread(target=trading_loop, args=(window, params), daemon=True).start()
        if event == "Stop":
            window.running = False
            next_update_time = None

        # Handle updates from trading_loop
        if event == "-UPDATE-":
            u = values["-UPDATE-"]
            window["-TIME-"].update(u["time"])
            window["-BAL-"].update(f"${u['balance']:.2f}")
            window["-PRI-"].update(f"${u['price']:.2f}")
            window["-SIG-"].update(u["signal"])
            window["-SCO-"].update(f"{u['score']:.2f}")
            window["-POS-"].update(u["position"])
            next_update_time = time.time() + params["poll_interval"]

    window.close()
