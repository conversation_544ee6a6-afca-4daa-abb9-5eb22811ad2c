#!/usr/bin/env python3
"""
Test Script for Live Trading System
Quick test to verify everything works before starting the full system
"""

import os
import sys
from datetime import datetime, timedelta, timezone
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_environment():
    """Test environment setup"""
    print("🔧 Testing Environment Setup...")
    
    # Check Binance API
    api_key = os.getenv("BINANCE_API_KEY")
    secret_key = os.getenv("BINANCE_SECRET_KEY")
    
    if api_key and secret_key:
        print("  ✅ Binance API credentials found")
    else:
        print("  ❌ Binance API credentials missing")
        print("     Add BINANCE_API_KEY and BINANCE_SECRET_KEY to .env file")
    
    # Check Telegram
    bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
    chat_id = os.getenv("TELEGRAM_CHAT_ID")
    
    if bot_token and chat_id:
        print("  ✅ Telegram credentials found")
    else:
        print("  ⚠️ Telegram credentials missing (optional)")
        print("     Add TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID to .env file")
    
    return bool(api_key and secret_key)

def test_binance_connection():
    """Test Binance API connection"""
    print("\n📡 Testing Binance Connection...")
    
    try:
        from binance.client import Client
        
        api_key = os.getenv("BINANCE_API_KEY")
        secret_key = os.getenv("BINANCE_SECRET_KEY")
        
        client = Client(api_key, secret_key)
        
        # Test getting current price
        ticker = client.get_symbol_ticker(symbol="ETHUSDT")
        price = float(ticker['price'])
        
        print(f"  ✅ Connection successful")
        print(f"  📊 Current ETHUSDT price: ${price:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Connection failed: {e}")
        return False

def test_strategy_system():
    """Test strategy system"""
    print("\n🧠 Testing Strategy System...")
    
    try:
        from enhanced_strategy_system import EnhancedStrategySystem
        
        system = EnhancedStrategySystem()
        
        # Test with small data sample
        end_time = datetime.now(timezone.utc)
        start_time = end_time - timedelta(hours=24)
        
        signals_df = system.create_adaptive_ensemble(
            'ETHUSDT', '1h', start_time, end_time
        )
        
        if len(signals_df) > 0:
            print(f"  ✅ Strategy system working")
            print(f"  📊 Generated {len(signals_df)} data points")
            
            # Check latest signal
            latest = signals_df.iloc[-1]
            print(f"  🎯 Latest signal: {latest['signal']}")
            print(f"  💪 Signal strength: {latest.get('signal_strength', 0):.2f}")
            print(f"  🌊 Market condition: {latest.get('market_condition', 'unknown')}")
            
            return True
        else:
            print("  ❌ No signals generated")
            return False
            
    except Exception as e:
        print(f"  ❌ Strategy system failed: {e}")
        return False

def test_telegram_notifications():
    """Test Telegram notifications"""
    print("\n📱 Testing Telegram Notifications...")
    
    try:
        from live_trading_with_telegram import TelegramNotifier
        
        telegram = TelegramNotifier()
        
        if telegram.enabled:
            # Send test message
            success = telegram.send_message("🧪 <b>Test Message</b>\n\nTelegram notifications are working!")
            
            if success:
                print("  ✅ Telegram test message sent successfully")
                print("  📱 Check your Telegram for the test message")
                return True
            else:
                print("  ❌ Failed to send Telegram message")
                return False
        else:
            print("  ⚠️ Telegram not configured (will use console output)")
            return True
            
    except Exception as e:
        print(f"  ❌ Telegram test failed: {e}")
        return False

def test_mock_trading():
    """Test mock trading functionality"""
    print("\n💰 Testing Mock Trading System...")
    
    try:
        from live_trading_with_telegram import MockTradingBot
        
        # Create bot with minimal settings
        bot = MockTradingBot(
            initial_capital=100,  # Small test amount
            risk_per_trade=0.01,
            max_positions=1,
            symbol='ETHUSDT',
            interval='1h'
        )
        
        print("  ✅ Mock trading bot created successfully")
        
        # Test signal checking
        signal_data = bot.check_signals()
        print(f"  🎯 Current signal: {signal_data.get('signal', 'UNKNOWN')}")
        print(f"  💪 Signal strength: {signal_data.get('strength', 0):.2f}")
        print(f"  💰 Current price: ${signal_data.get('price', 0):,.2f}")
        
        # Test performance summary
        performance = bot.get_performance_summary()
        print(f"  📊 Initial balance: ${performance['current_balance']:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Mock trading test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧪 LIVE TRADING SYSTEM - COMPREHENSIVE TEST")
    print("=" * 60)
    
    tests = [
        ("Environment Setup", test_environment),
        ("Binance Connection", test_binance_connection),
        ("Strategy System", test_strategy_system),
        ("Telegram Notifications", test_telegram_notifications),
        ("Mock Trading", test_mock_trading)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"  ❌ {test_name} crashed: {e}")
            results[test_name] = False
    
    # Summary
    print("\n" + "=" * 60)
    print("🏁 TEST SUMMARY")
    print("=" * 60)
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<25}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Your system is ready for live trading!")
        print("\n🚀 To start the live trading system:")
        print("   python live_trading_with_telegram.py")
        
    elif passed >= 3:
        print("\n⚠️ Most tests passed - system should work with minor issues")
        print("✅ You can start the live trading system")
        print("🔧 Fix the failed tests for optimal performance")
        
    else:
        print("\n❌ Multiple tests failed - please fix issues before starting")
        print("🔧 Check your .env file and API credentials")
        print("📖 Refer to COMPLETE_SETUP_GUIDE.md for help")
    
    return results

if __name__ == "__main__":
    results = main()
