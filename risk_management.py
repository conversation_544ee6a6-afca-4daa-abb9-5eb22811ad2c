#!/usr/bin/env python3
"""
Advanced Risk Management System for Crypto Trading
Implements adaptive position sizing, dynamic stops, and portfolio protection
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from datetime import datetime, timedelta

class AdvancedRiskManager:
    """
    Advanced risk management with adaptive position sizing and dynamic stops
    """
    
    def __init__(self, initial_capital: float = 10000, max_risk_per_trade: float = 0.02,
                 max_portfolio_risk: float = 0.06, max_drawdown_limit: float = 0.15):
        self.initial_capital = initial_capital
        self.max_risk_per_trade = max_risk_per_trade
        self.max_portfolio_risk = max_portfolio_risk
        self.max_drawdown_limit = max_drawdown_limit
        
        # Risk state tracking
        self.current_drawdown = 0.0
        self.consecutive_losses = 0
        self.peak_equity = initial_capital
        self.risk_reduction_factor = 1.0
        
    def calculate_position_size(self, df: pd.DataFrame, current_capital: float,
                              signal_strength: float, volatility_rank: float) -> float:
        """
        Calculate adaptive position size based on multiple risk factors
        """
        # Base risk per trade
        base_risk = min(self.max_risk_per_trade, self.max_portfolio_risk / 3)
        
        # Adjust for current drawdown
        drawdown_factor = self._get_drawdown_adjustment()
        
        # Adjust for signal strength (0.5x to 1.5x)
        strength_factor = 0.5 + (signal_strength * 1.0)
        strength_factor = np.clip(strength_factor, 0.5, 1.5)
        
        # Adjust for volatility (inverse relationship)
        volatility_factor = 1.5 - volatility_rank
        volatility_factor = np.clip(volatility_factor, 0.5, 1.5)
        
        # Adjust for consecutive losses
        loss_factor = max(0.5, 1.0 - (self.consecutive_losses * 0.1))
        
        # Calculate final position size
        adjusted_risk = base_risk * drawdown_factor * strength_factor * volatility_factor * loss_factor
        position_size = current_capital * adjusted_risk

        # Ensure we return a scalar value
        if hasattr(position_size, 'iloc'):
            position_size = position_size.iloc[-1] if len(position_size) > 0 else current_capital * 0.005

        return max(float(position_size), current_capital * 0.005)  # Minimum 0.5% position
    
    def calculate_stop_loss(self, df: pd.DataFrame, entry_price: float, 
                           signal_type: str, signal_strength: float) -> Dict:
        """
        Calculate adaptive stop-loss and take-profit levels
        """
        current_atr = df['atr'].iloc[-1] if 'atr' in df.columns else entry_price * 0.02
        
        # Base ATR multiplier adjusted by signal strength
        base_multiplier = 1.5
        strength_adjustment = 0.5 + (signal_strength * 1.0)  # 0.5x to 1.5x
        
        # Volatility adjustment
        if 'volatility_rank' in df.columns:
            vol_rank = df['volatility_rank'].iloc[-1]
            vol_adjustment = 1.0 + (vol_rank * 0.5)  # 1.0x to 1.5x for high vol
        else:
            vol_adjustment = 1.0
        
        # Market regime adjustment
        if 'regime' in df.columns:
            regime = df['regime'].iloc[-1]
            regime_multiplier = {
                'TRENDING': 1.2,
                'RANGING': 0.8,
                'VOLATILE': 1.5
            }.get(regime, 1.0)
        else:
            regime_multiplier = 1.0
        
        final_multiplier = base_multiplier * strength_adjustment * vol_adjustment * regime_multiplier
        
        if signal_type == 'BUY':
            stop_loss = entry_price - (final_multiplier * current_atr)
            take_profit = entry_price + (final_multiplier * current_atr * 2.5)  # 2.5:1 R/R
        else:  # SELL
            stop_loss = entry_price + (final_multiplier * current_atr)
            take_profit = entry_price - (final_multiplier * current_atr * 2.5)
        
        return {
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'atr_multiplier': final_multiplier,
            'risk_reward_ratio': 2.5
        }
    
    def update_risk_state(self, current_equity: float, trade_pnl: float = None):
        """
        Update risk management state based on current performance
        """
        # Update peak equity
        if current_equity > self.peak_equity:
            self.peak_equity = current_equity
            self.consecutive_losses = 0  # Reset on new high
        
        # Calculate current drawdown
        self.current_drawdown = (self.peak_equity - current_equity) / self.peak_equity
        
        # Update consecutive losses
        if trade_pnl is not None:
            if trade_pnl < 0:
                self.consecutive_losses += 1
            else:
                self.consecutive_losses = 0
        
        # Update risk reduction factor
        self.risk_reduction_factor = self._get_drawdown_adjustment()
    
    def _get_drawdown_adjustment(self) -> float:
        """
        Calculate risk reduction factor based on current drawdown
        """
        if self.current_drawdown < 0.05:  # Less than 5% drawdown
            return 1.0
        elif self.current_drawdown < 0.10:  # 5-10% drawdown
            return 0.8
        elif self.current_drawdown < 0.15:  # 10-15% drawdown
            return 0.6
        else:  # More than 15% drawdown
            return 0.4
    
    def should_stop_trading(self) -> bool:
        """
        Determine if trading should be halted due to excessive losses
        """
        return (self.current_drawdown > self.max_drawdown_limit or 
                self.consecutive_losses > 8)
    
    def get_risk_metrics(self) -> Dict:
        """
        Get current risk management metrics
        """
        return {
            'current_drawdown': self.current_drawdown,
            'consecutive_losses': self.consecutive_losses,
            'risk_reduction_factor': self.risk_reduction_factor,
            'peak_equity': self.peak_equity,
            'should_stop_trading': self.should_stop_trading()
        }

class PortfolioManager:
    """
    Portfolio-level risk management and position tracking
    """
    
    def __init__(self, max_positions: int = 3, correlation_threshold: float = 0.7):
        self.max_positions = max_positions
        self.correlation_threshold = correlation_threshold
        self.active_positions = {}
        self.position_history = []
    
    def can_open_position(self, symbol: str, signal_type: str) -> bool:
        """
        Check if a new position can be opened based on portfolio constraints
        """
        # Check maximum positions
        if len(self.active_positions) >= self.max_positions:
            return False
        
        # Check for conflicting signals on same symbol
        if symbol in self.active_positions:
            existing_signal = self.active_positions[symbol]['signal_type']
            if existing_signal != signal_type:
                return False  # Don't open opposing position
        
        # Check correlation with existing positions (simplified)
        # In practice, you'd calculate actual correlation between assets
        similar_positions = sum(1 for pos in self.active_positions.values() 
                              if pos['signal_type'] == signal_type)
        
        if similar_positions >= 2:  # Max 2 positions in same direction
            return False
        
        return True
    
    def add_position(self, symbol: str, signal_type: str, entry_price: float,
                    position_size: float, stop_loss: float, take_profit: float):
        """
        Add a new position to the portfolio
        """
        self.active_positions[symbol] = {
            'signal_type': signal_type,
            'entry_price': entry_price,
            'position_size': position_size,
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'entry_time': datetime.now()
        }
    
    def remove_position(self, symbol: str, exit_price: float, exit_reason: str):
        """
        Remove a position from the portfolio
        """
        if symbol in self.active_positions:
            position = self.active_positions.pop(symbol)
            
            # Calculate P&L
            if position['signal_type'] == 'BUY':
                pnl = (exit_price - position['entry_price']) * position['position_size']
            else:
                pnl = (position['entry_price'] - exit_price) * position['position_size']
            
            # Record in history
            self.position_history.append({
                'symbol': symbol,
                'signal_type': position['signal_type'],
                'entry_price': position['entry_price'],
                'exit_price': exit_price,
                'position_size': position['position_size'],
                'pnl': pnl,
                'exit_reason': exit_reason,
                'entry_time': position['entry_time'],
                'exit_time': datetime.now()
            })
            
            return pnl
        return 0
    
    def get_portfolio_risk(self) -> float:
        """
        Calculate current portfolio risk exposure
        """
        total_risk = sum(pos['position_size'] for pos in self.active_positions.values())
        return total_risk
    
    def get_portfolio_summary(self) -> Dict:
        """
        Get portfolio summary statistics
        """
        if not self.position_history:
            return {'total_trades': 0, 'win_rate': 0, 'avg_pnl': 0}
        
        pnls = [trade['pnl'] for trade in self.position_history]
        winning_trades = [pnl for pnl in pnls if pnl > 0]
        
        return {
            'total_trades': len(self.position_history),
            'win_rate': len(winning_trades) / len(pnls) if pnls else 0,
            'avg_pnl': np.mean(pnls) if pnls else 0,
            'total_pnl': sum(pnls),
            'active_positions': len(self.active_positions),
            'max_positions': self.max_positions
        }

def calculate_kelly_criterion(win_rate: float, avg_win: float, avg_loss: float) -> float:
    """
    Calculate Kelly Criterion for optimal position sizing
    """
    if avg_loss == 0 or win_rate == 0:
        return 0.01  # Conservative default
    
    win_loss_ratio = abs(avg_win / avg_loss)
    kelly_fraction = win_rate - ((1 - win_rate) / win_loss_ratio)
    
    # Cap at 25% for safety
    return max(0.01, min(0.25, kelly_fraction))

if __name__ == "__main__":
    # Test the risk management system
    risk_manager = AdvancedRiskManager(initial_capital=10000)
    portfolio_manager = PortfolioManager()
    
    print("Risk Management System Test:")
    print(f"Initial metrics: {risk_manager.get_risk_metrics()}")
    
    # Simulate some trades
    test_df = pd.DataFrame({
        'atr': [100, 105, 110],
        'volatility_rank': [0.5, 0.6, 0.7],
        'regime': ['TRENDING', 'TRENDING', 'RANGING']
    })
    
    position_size = risk_manager.calculate_position_size(test_df, 10000, 0.8, 0.5)
    stops = risk_manager.calculate_stop_loss(test_df, 50000, 'BUY', 0.8)
    
    print(f"Position size: ${position_size:.2f}")
    print(f"Stop loss: ${stops['stop_loss']:.2f}")
    print(f"Take profit: ${stops['take_profit']:.2f}")
