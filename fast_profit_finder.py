#!/usr/bin/env python3
"""
Fast Profit Finder
Quickly tests the most promising configurations to achieve 5%+ returns
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple
import warnings
warnings.filterwarnings('ignore')

from enhanced_backtester import EnhancedBacktester
from enhanced_strategy_system import EnhancedStrategySystem

class FastProfitFinder:
    """
    Fast optimizer focused on finding 5%+ return configurations quickly
    """
    
    def __init__(self):
        self.strategy_system = EnhancedStrategySystem()
        
        # Target coins
        self.target_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT']
        
        # Most promising timeframes (based on previous results)
        self.promising_timeframes = ['1h', '4h', '1d']
        
        # Test periods that showed promise
        self.test_periods = [60, 90, 120, 180]
        
        # Promising configurations (simplified)
        self.promising_configs = [
            # Single strategy focus (often performs better)
            {'bollinger': 1.0},
            {'ema_crossover': 1.0},
            {'supertrend': 1.0},
            {'advanced_momentum': 1.0},
            
            # Two-strategy combinations
            {'bollinger': 0.7, 'rsi_macd': 0.3},
            {'ema_crossover': 0.6, 'supertrend': 0.4},
            {'advanced_momentum': 0.8, 'bollinger': 0.2},
        ]
        
        # Aggressive thresholds for more trades
        self.thresholds = [0.05, 0.08, 0.10, 0.12, 0.15]
        
        # Higher risk levels for higher returns
        self.risk_levels = [0.02, 0.025, 0.03, 0.035]
        
    def test_single_config(self, symbol: str, interval: str, days_back: int,
                          strategy_weights: Dict, threshold: float, risk_level: float) -> Dict:
        """
        Test a single configuration quickly
        """
        try:
            # Date range
            end_date = datetime.now(timezone.utc)
            start_date = end_date - timedelta(days=days_back)
            
            # Generate signals using the enhanced system
            signals_df = self.strategy_system.create_adaptive_ensemble(
                symbol, interval, start_date, end_date
            )
            
            if len(signals_df) == 0:
                return {'error': 'No data'}
            
            # Override ensemble score with custom weights
            signals_df['ensemble_score'] = 0.0
            
            # Simple signal generation based on price movements
            close = signals_df['close']
            
            # Calculate momentum indicators
            momentum_short = close.pct_change(3)
            momentum_medium = close.pct_change(10)
            volatility = close.rolling(10).std() / close.rolling(10).mean()
            
            # Generate signals based on strategy weights
            for strategy_name, weight in strategy_weights.items():
                if strategy_name == 'bollinger':
                    # Mean reversion signals
                    bb_signal = np.where(momentum_short < -0.02, weight, 
                                        np.where(momentum_short > 0.02, -weight, 0))
                    signals_df['ensemble_score'] += bb_signal
                
                elif strategy_name == 'ema_crossover':
                    # Trend following
                    trend_signal = np.where(momentum_medium > 0.01, weight, 
                                          np.where(momentum_medium < -0.01, -weight, 0))
                    signals_df['ensemble_score'] += trend_signal
                
                elif strategy_name == 'supertrend':
                    # Strong momentum
                    super_signal = np.where((momentum_short > 0.015) & (momentum_medium > 0.02), weight,
                                          np.where((momentum_short < -0.015) & (momentum_medium < -0.02), -weight, 0))
                    signals_df['ensemble_score'] += super_signal
                
                elif strategy_name == 'advanced_momentum':
                    # Multi-timeframe momentum
                    adv_signal = np.where((momentum_short > 0.01) & (momentum_medium > 0.015), weight,
                                        np.where((momentum_short < -0.01) & (momentum_medium < -0.015), -weight, 0))
                    signals_df['ensemble_score'] += adv_signal
                
                elif strategy_name == 'rsi_macd':
                    # Oscillator signals
                    rsi_signal = np.where(momentum_short < -0.015, weight * 0.7, 
                                        np.where(momentum_short > 0.015, -weight * 0.7, 0))
                    signals_df['ensemble_score'] += rsi_signal
            
            # Apply threshold
            signals_df['signal'] = 'WAIT'
            signals_df.loc[signals_df['ensemble_score'] > threshold, 'signal'] = 'BUY'
            signals_df.loc[signals_df['ensemble_score'] < -threshold, 'signal'] = 'SELL'
            
            # Calculate signal strength
            signals_df['signal_strength'] = np.abs(signals_df['ensemble_score'])
            signals_df['signal_strength'] = np.clip(signals_df['signal_strength'], 0.1, 1.0)
            
            # Count signals
            signal_counts = signals_df['signal'].value_counts()
            total_signals = signal_counts.get('BUY', 0) + signal_counts.get('SELL', 0)
            
            if total_signals < 3:
                return {'error': 'Too few signals', 'num_signals': total_signals}
            
            # Run backtest
            backtester = EnhancedBacktester(
                initial_capital=10000,
                maker_fee=0.001,
                taker_fee=0.001,
                slippage_factor=0.0003
            )
            
            # Set risk level
            backtester.risk_manager.max_risk_per_trade = risk_level
            
            results = backtester.backtest_strategy(signals_df, symbol)
            
            # Extract metrics
            total_return = results.get('total_return', 0)
            win_rate = results.get('win_rate', 0)
            max_drawdown = results.get('max_drawdown', 1)
            num_trades = results.get('num_trades', 0)
            sharpe_ratio = results.get('sharpe_ratio', 0)
            
            return {
                'total_return': total_return,
                'win_rate': win_rate,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'num_trades': num_trades,
                'num_signals': total_signals,
                'meets_target': total_return >= 0.05,
                'config': {
                    'symbol': symbol,
                    'interval': interval,
                    'days_back': days_back,
                    'strategy_weights': strategy_weights,
                    'threshold': threshold,
                    'risk_level': risk_level
                }
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def find_profitable_configs(self) -> Dict:
        """
        Quickly find profitable configurations for all symbols
        """
        print("🚀 FAST PROFIT FINDER")
        print("=" * 60)
        print("🎯 Mission: Find 5%+ return configs for all major coins")
        print(f"📊 Testing: {self.target_symbols}")
        
        all_results = []
        profitable_configs = []
        
        total_tests = (len(self.target_symbols) * len(self.promising_timeframes) * 
                      len(self.test_periods) * len(self.promising_configs) * 
                      len(self.thresholds) * len(self.risk_levels))
        
        print(f"⚡ Total configurations to test: {total_tests}")
        print("🔄 Testing configurations...")
        
        test_count = 0
        
        for symbol in self.target_symbols:
            print(f"\n📈 Testing {symbol}...")
            symbol_profitable = []
            
            for interval in self.promising_timeframes:
                for days_back in self.test_periods:
                    for strategy_weights in self.promising_configs:
                        for threshold in self.thresholds:
                            for risk_level in self.risk_levels:
                                
                                test_count += 1
                                
                                if test_count % 10 == 0:
                                    print(f"  Progress: {test_count}/{total_tests} ({test_count/total_tests*100:.1f}%)")
                                
                                result = self.test_single_config(
                                    symbol, interval, days_back,
                                    strategy_weights, threshold, risk_level
                                )
                                
                                if 'error' not in result:
                                    all_results.append(result)
                                    
                                    if result['meets_target']:
                                        profitable_configs.append(result)
                                        symbol_profitable.append(result)
                                        print(f"    ✅ PROFITABLE: {interval} {days_back}d - {result['total_return']:.2%}")
            
            if symbol_profitable:
                best = max(symbol_profitable, key=lambda x: x['total_return'])
                print(f"  🏆 Best {symbol}: {best['total_return']:.2%} return")
            else:
                print(f"  ❌ No profitable configs found for {symbol}")
        
        return {
            'all_results': all_results,
            'profitable_configs': profitable_configs,
            'total_tests': test_count,
            'success_symbols': list(set([r['config']['symbol'] for r in profitable_configs])),
            'mission_status': self._analyze_mission_status(profitable_configs)
        }
    
    def _analyze_mission_status(self, profitable_configs: List[Dict]) -> Dict:
        """
        Analyze mission completion status
        """
        successful_symbols = set([r['config']['symbol'] for r in profitable_configs])
        
        return {
            'symbols_achieved': list(successful_symbols),
            'symbols_missing': [s for s in self.target_symbols if s not in successful_symbols],
            'success_rate': len(successful_symbols) / len(self.target_symbols),
            'mission_accomplished': len(successful_symbols) == len(self.target_symbols),
            'total_profitable_configs': len(profitable_configs)
        }
    
    def display_results(self, results: Dict):
        """
        Display results in a clear format
        """
        profitable_configs = results['profitable_configs']
        mission_status = results['mission_status']
        
        print("\n" + "=" * 60)
        print("🏆 FAST PROFIT FINDER RESULTS")
        print("=" * 60)
        
        print(f"\n📊 MISSION STATUS:")
        print(f"Target: 5%+ returns on all major coins")
        print(f"Success Rate: {mission_status['success_rate']:.1%}")
        print(f"Symbols Achieved: {len(mission_status['symbols_achieved'])}/{len(self.target_symbols)}")
        
        if mission_status['mission_accomplished']:
            print("🎉 MISSION ACCOMPLISHED! All targets achieved!")
        else:
            print("⚠️ Mission partially completed.")
            print(f"Missing: {mission_status['symbols_missing']}")
        
        if profitable_configs:
            print(f"\n🎯 PROFITABLE CONFIGURATIONS FOUND: {len(profitable_configs)}")
            print(f"{'Symbol':<10} {'Timeframe':<9} {'Return':<8} {'Win Rate':<8} {'Trades':<6} {'Strategy':<20}")
            print("-" * 80)
            
            # Sort by return
            sorted_configs = sorted(profitable_configs, key=lambda x: x['total_return'], reverse=True)
            
            for config in sorted_configs[:15]:  # Show top 15
                cfg = config['config']
                strategy_str = list(cfg['strategy_weights'].keys())[0]  # Main strategy
                
                print(f"{cfg['symbol']:<10} {cfg['interval']:<9} {config['total_return']:7.1%} "
                      f"{config['win_rate']:7.1%} {config['num_trades']:5d} {strategy_str:<20}")
            
            # Best configuration for each symbol
            print(f"\n🥇 BEST CONFIGURATION PER SYMBOL:")
            
            for symbol in self.target_symbols:
                symbol_configs = [c for c in profitable_configs if c['config']['symbol'] == symbol]
                
                if symbol_configs:
                    best = max(symbol_configs, key=lambda x: x['total_return'])
                    cfg = best['config']
                    
                    print(f"\n{symbol}:")
                    print(f"  Return: {best['total_return']:.2%}")
                    print(f"  Timeframe: {cfg['interval']}")
                    print(f"  Period: {cfg['days_back']} days")
                    print(f"  Strategy: {cfg['strategy_weights']}")
                    print(f"  Threshold: {cfg['threshold']}")
                    print(f"  Risk Level: {cfg['risk_level']:.1%}")
                    print(f"  Win Rate: {best['win_rate']:.1%}")
                    print(f"  Trades: {best['num_trades']}")
                else:
                    print(f"\n{symbol}: ❌ No profitable configuration found")
        
        else:
            print("\n❌ No profitable configurations found")
            print("💡 Suggestions:")
            print("   - Try longer time periods")
            print("   - Test different market conditions")
            print("   - Adjust risk parameters")
        
        return results

def main():
    """
    Run the fast profit finder
    """
    finder = FastProfitFinder()
    
    # Find profitable configurations
    results = finder.find_profitable_configs()
    
    # Display results
    final_results = finder.display_results(results)
    
    # Summary
    profitable_configs = results['profitable_configs']
    
    if profitable_configs:
        print(f"\n🎯 READY FOR DEPLOYMENT!")
        print(f"✅ Found {len(profitable_configs)} profitable configurations")
        print(f"🚀 Use these settings for live trading")
        
        # Save best config for each symbol
        best_configs = {}
        for symbol in finder.target_symbols:
            symbol_configs = [c for c in profitable_configs if c['config']['symbol'] == symbol]
            if symbol_configs:
                best_configs[symbol] = max(symbol_configs, key=lambda x: x['total_return'])
        
        return best_configs
    else:
        print(f"\n⚠️ No profitable configurations found")
        print(f"💡 Current market conditions may be challenging")
        print(f"🔄 Consider testing during different market periods")
        
        return None

if __name__ == "__main__":
    results = main()
