#!/usr/bin/env python3
"""
Enhanced Walk-Forward Analysis with Proper Validation
Addresses overfitting and provides more robust strategy evaluation
"""

import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# Import the enhanced strategy system
from enhanced_trading_system import EnhancedMultiStrategy, get_enhanced_config

class RobustWalkForward:
    """Enhanced walk-forward analysis with proper validation"""
    
    def __init__(self, min_trades_threshold: int = 10):
        self.min_trades_threshold = min_trades_threshold
        self.strategy = EnhancedMultiStrategy()
        
    def monte_carlo_validation(self, equity_curve: List[float], 
                              num_simulations: int = 1000) -> Dict:
        """
        Monte Carlo validation of strategy performance
        """
        returns = np.diff(equity_curve) / equity_curve[:-1]
        
        # Generate random return sequences
        simulated_returns = []
        for _ in range(num_simulations):
            # Bootstrap resampling
            sim_returns = np.random.choice(returns, size=len(returns), replace=True)
            simulated_returns.append(sim_returns)
        
        # Calculate performance metrics for each simulation
        sim_final_values = []
        sim_sharpe_ratios = []
        sim_max_drawdowns = []
        
        for sim_ret in simulated_returns:
            sim_equity = [equity_curve[0]]
            for ret in sim_ret:
                sim_equity.append(sim_equity[-1] * (1 + ret))
            
            # Final value
            sim_final_values.append(sim_equity[-1])
            
            # Sharpe ratio
            if np.std(sim_ret) > 0:
                sim_sharpe_ratios.append(np.mean(sim_ret) / np.std(sim_ret) * np.sqrt(252))
            else:
                sim_sharpe_ratios.append(0)
            
            # Max drawdown
            equity_array = np.array(sim_equity)
            running_max = np.maximum.accumulate(equity_array)
            drawdown = (equity_array - running_max) / running_max
            sim_max_drawdowns.append(np.min(drawdown))
        
        # Calculate percentiles
        actual_final = equity_curve[-1]
        actual_sharpe = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        equity_array = np.array(equity_curve)
        running_max = np.maximum.accumulate(equity_array)
        drawdown = (equity_array - running_max) / running_max
        actual_drawdown = np.min(drawdown)
        
        return {
            'final_value_percentile': stats.percentileofscore(sim_final_values, actual_final),
            'sharpe_percentile': stats.percentileofscore(sim_sharpe_ratios, actual_sharpe),
            'drawdown_percentile': stats.percentileofscore(sim_max_drawdowns, actual_drawdown),
            'simulated_final_values': sim_final_values,
            'simulated_sharpe_ratios': sim_sharpe_ratios,
            'simulated_max_drawdowns': sim_max_drawdowns
        }
    
    def calculate_robust_metrics(self, equity_curve: List[float], 
                               trades: List[Dict]) -> Dict:
        """
        Calculate robust performance metrics
        """
        if len(equity_curve) < 2:
            return {'error': 'Insufficient data'}
        
        equity_array = np.array(equity_curve)
        returns = np.diff(equity_array) / equity_array[:-1]
        
        # Basic metrics
        total_return = (equity_array[-1] - equity_array[0]) / equity_array[0]
        
        # Risk metrics
        volatility = np.std(returns) * np.sqrt(252)  # Annualized
        sharpe_ratio = np.mean(returns) / np.std(returns) * np.sqrt(252) if np.std(returns) > 0 else 0
        
        # Drawdown analysis
        running_max = np.maximum.accumulate(equity_array)
        drawdown = (equity_array - running_max) / running_max
        max_drawdown = np.min(drawdown)
        
        # Drawdown duration
        drawdown_periods = []
        in_drawdown = False
        start_dd = 0
        
        for i, dd in enumerate(drawdown):
            if dd < -0.01 and not in_drawdown:  # Start of drawdown (1% threshold)
                in_drawdown = True
                start_dd = i
            elif dd >= -0.01 and in_drawdown:  # End of drawdown
                in_drawdown = False
                drawdown_periods.append(i - start_dd)
        
        avg_drawdown_duration = np.mean(drawdown_periods) if drawdown_periods else 0
        
        # Trade analysis
        if trades:
            trade_returns = [t['pnl'] / 10000 for t in trades]  # Normalize by initial capital
            win_rate = len([t for t in trade_returns if t > 0]) / len(trade_returns)
            
            winning_trades = [t for t in trade_returns if t > 0]
            losing_trades = [t for t in trade_returns if t < 0]
            
            avg_win = np.mean(winning_trades) if winning_trades else 0
            avg_loss = np.mean(losing_trades) if losing_trades else 0
            
            profit_factor = abs(avg_win / avg_loss) if avg_loss < 0 else float('inf')
            
            # Expectancy
            expectancy = win_rate * avg_win + (1 - win_rate) * avg_loss
            
            # Consecutive wins/losses
            consecutive_wins = []
            consecutive_losses = []
            current_win_streak = 0
            current_loss_streak = 0
            
            for ret in trade_returns:
                if ret > 0:
                    current_win_streak += 1
                    if current_loss_streak > 0:
                        consecutive_losses.append(current_loss_streak)
                        current_loss_streak = 0
                else:
                    current_loss_streak += 1
                    if current_win_streak > 0:
                        consecutive_wins.append(current_win_streak)
                        current_win_streak = 0
            
            # Add final streak
            if current_win_streak > 0:
                consecutive_wins.append(current_win_streak)
            if current_loss_streak > 0:
                consecutive_losses.append(current_loss_streak)
            
            max_consecutive_wins = max(consecutive_wins) if consecutive_wins else 0
            max_consecutive_losses = max(consecutive_losses) if consecutive_losses else 0
            
            # Kelly Criterion
            if avg_loss < 0 and avg_win > 0:
                b = abs(avg_win / avg_loss)  # Odds received on the wager
                p = win_rate  # Probability of winning
                kelly_fraction = (b * p - (1 - p)) / b
            else:
                kelly_fraction = 0
            
            # Ulcer Index
            ulcer_index = np.sqrt(np.mean(drawdown ** 2)) if len(drawdown) > 0 else 0
            
            # Calmar Ratio
            calmar_ratio = (total_return / abs(max_drawdown)) if max_drawdown != 0 else 0
            
            # Sortino Ratio
            downside_returns = [r for r in returns if r < 0]
            downside_deviation = np.std(downside_returns) * np.sqrt(252) if downside_returns else 0
            sortino_ratio = np.mean(returns) / downside_deviation * np.sqrt(252) if downside_deviation > 0 else 0
            
            return {
                'total_return': total_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'sortino_ratio': sortino_ratio,
                'calmar_ratio': calmar_ratio,
                'max_drawdown': max_drawdown,
                'avg_drawdown_duration': avg_drawdown_duration,
                'ulcer_index': ulcer_index,
                'win_rate': win_rate,
                'profit_factor': profit_factor,
                'expectancy': expectancy,
                'avg_win': avg_win,
                'avg_loss': avg_loss,
                'max_consecutive_wins': max_consecutive_wins,
                'max_consecutive_losses': max_consecutive_losses,
                'kelly_fraction': kelly_fraction,
                'total_trades': len(trades)
            }
        
        return {
            'total_return': total_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'avg_drawdown_duration': avg_drawdown_duration,
            'total_trades': 0
        }
    
    def run_walk_forward(self, data: pd.DataFrame, 
                        lookback_days: int = 90,
                        forward_days: int = 30,
                        min_data_points: int = 100) -> Dict:
        """
        Run enhanced walk-forward analysis
        """
        print("Starting Enhanced Walk-Forward Analysis...")
        
        # Ensure data is sorted by datetime
        data = data.sort_values('datetime').reset_index(drop=True)
        
        # Calculate total periods
        total_days = len(data)
        if total_days < lookback_days + forward_days:
            return {'error': 'Insufficient data for walk-forward analysis'}
        
        # Initialize results storage
        results = {
            'periods': [],
            'equity_curves': [],
            'trades': [],
            'metrics': [],
            'parameter_stability': [],
            'regime_analysis': []
        }
        
        # Walk-forward loop
        current_start = 0
        period_num = 0
        
        while current_start + lookback_days + forward_days <= total_days:
            print(f"Processing period {period_num + 1}...")
            
            # Define periods
            train_start = current_start
            train_end = current_start + lookback_days
            test_start = train_end
            test_end = min(test_start + forward_days, total_days)
            
            # Extract data
            train_data = data.iloc[train_start:train_end].copy()
            test_data = data.iloc[test_start:test_end].copy()
            
            # Skip if insufficient data
            if len(train_data) < min_data_points or len(test_data) < 10:
                current_start += forward_days
                continue
            
            # Optimize parameters on training data
            print(f"  Optimizing parameters on training data...")
            best_params = self.optimize_parameters(train_data)
            
            # Test on out-of-sample data
            print(f"  Testing on out-of-sample data...")
            test_results = self.run_strategy_test(test_data, best_params)
            
            # Store results
            period_info = {
                'period': period_num,
                'train_start': train_data.iloc[0]['datetime'],
                'train_end': train_data.iloc[-1]['datetime'],
                'test_start': test_data.iloc[0]['datetime'],
                'test_end': test_data.iloc[-1]['datetime'],
                'best_params': best_params,
                'train_size': len(train_data),
                'test_size': len(test_data)
            }
            
            results['periods'].append(period_info)
            results['equity_curves'].append(test_results['equity_curve'])
            results['trades'].append(test_results['trades'])
            results['metrics'].append(test_results['metrics'])
            
            # Move to next period
            current_start += forward_days
            period_num += 1
        
        # Aggregate results
        print("Aggregating results...")
        aggregated_results = self.aggregate_results(results)
        
        # Monte Carlo validation
        print("Running Monte Carlo validation...")
        if aggregated_results['combined_equity_curve']:
            mc_results = self.monte_carlo_validation(aggregated_results['combined_equity_curve'])
            aggregated_results['monte_carlo'] = mc_results
        
        return aggregated_results
    
    def optimize_parameters(self, data: pd.DataFrame) -> Dict:
        """
        Optimize strategy parameters using training data
        """
        best_params = {}
        best_score = -np.inf
        
        # Define parameter ranges for optimization
        param_ranges = {
            'rsi_period': [12, 14, 16, 18, 20],
            'rsi_oversold': [25, 30, 35],
            'rsi_overbought': [65, 70, 75],
            'macd_fast': [10, 12, 14],
            'macd_slow': [24, 26, 28],
            'macd_signal': [8, 9, 10],
            'ema_fast': [8, 10, 12],
            'ema_slow': [20, 21, 22],
            'bb_period': [18, 20, 22],
            'bb_std': [1.8, 2.0, 2.2],
            'stoch_k': [12, 14, 16],
            'stoch_d': [3, 5, 7],
            'supertrend_period': [8, 10, 12],
            'supertrend_multiplier': [2.8, 3.0, 3.2],
            'atr_period': [12, 14, 16],
            'stop_loss_atr': [2.0, 2.5, 3.0],
            'take_profit_atr': [3.0, 4.0, 5.0]
        }
        
        # Random search for parameter optimization
        max_iterations = 100
        
        for iteration in range(max_iterations):
            # Generate random parameter combination
            test_params = {}
            for param, values in param_ranges.items():
                test_params[param] = np.random.choice(values)
            
            # Test parameters
            try:
                test_results = self.run_strategy_test(data, test_params)
                
                # Calculate fitness score (weighted combination of metrics)
                metrics = test_results['metrics']
                if metrics['total_trades'] >= self.min_trades_threshold:
                    # Multi-objective fitness function
                    score = (
                        0.3 * metrics['sharpe_ratio'] +
                        0.2 * metrics['total_return'] +
                        0.2 * (1 - abs(metrics['max_drawdown'])) +
                        0.15 * metrics['profit_factor'] +
                        0.15 * metrics['win_rate']
                    )
                    
                    if score > best_score:
                        best_score = score
                        best_params = test_params.copy()
                        
            except Exception as e:
                print(f"Error in parameter optimization: {e}")
                continue
        
        return best_params if best_params else self.get_default_params()
    
    def get_default_params(self) -> Dict:
        """Get default parameters"""
        return {
            'rsi_period': 14,
            'rsi_oversold': 30,
            'rsi_overbought': 70,
            'macd_fast': 12,
            'macd_slow': 26,
            'macd_signal': 9,
            'ema_fast': 10,
            'ema_slow': 21,
            'bb_period': 20,
            'bb_std': 2.0,
            'stoch_k': 14,
            'stoch_d': 3,
            'supertrend_period': 10,
            'supertrend_multiplier': 3.0,
            'atr_period': 14,
            'stop_loss_atr': 2.5,
            'take_profit_atr': 4.0
        }
    
    def run_strategy_test(self, data: pd.DataFrame, params: Dict) -> Dict:
        """
        Run strategy test with given parameters
        """
        # Initialize strategy with parameters
        config = get_enhanced_config()
        config.update(params)
        
        strategy = EnhancedMultiStrategy(config)
        
        # Run strategy
        results = strategy.run_backtest(data)
        
        # Calculate metrics
        metrics = self.calculate_robust_metrics(results['equity_curve'], results['trades'])
        
        return {
            'equity_curve': results['equity_curve'],
            'trades': results['trades'],
            'metrics': metrics,
            'signals': results.get('signals', [])
        }
    
    def aggregate_results(self, results: Dict) -> Dict:
        """
        Aggregate walk-forward results
        """
        # Combine equity curves
        combined_equity = [10000]  # Starting capital
        all_trades = []
        all_metrics = []
        
        for i, equity_curve in enumerate(results['equity_curves']):
            if equity_curve and len(equity_curve) > 1:
                # Scale equity curve to start from last value
                start_value = combined_equity[-1]
                normalized_curve = []
                
                for j, value in enumerate(equity_curve):
                    if j == 0:
                        normalized_curve.append(start_value)
                    else:
                        change = (value - equity_curve[j-1]) / equity_curve[j-1]
                        normalized_curve.append(normalized_curve[-1] * (1 + change))
                
                combined_equity.extend(normalized_curve[1:])
                all_trades.extend(results['trades'][i])
                all_metrics.append(results['metrics'][i])
        
        # Calculate combined metrics
        combined_metrics = self.calculate_robust_metrics(combined_equity, all_trades)
        
        # Parameter stability analysis
        stability_analysis = self.analyze_parameter_stability(results['periods'])
        
        return {
            'combined_equity_curve': combined_equity,
            'combined_trades': all_trades,
            'combined_metrics': combined_metrics,
            'period_metrics': all_metrics,
            'periods': results['periods'],
            'stability_analysis': stability_analysis,
            'total_periods': len(results['periods'])
        }
    
    def analyze_parameter_stability(self, periods: List[Dict]) -> Dict:
        """
        Analyze parameter stability across periods
        """
        if not periods:
            return {'error': 'No periods to analyze'}
        
        # Extract parameters from each period
        all_params = {}
        for period in periods:
            for param, value in period['best_params'].items():
                if param not in all_params:
                    all_params[param] = []
                all_params[param].append(value)
        
        # Calculate stability metrics
        stability_metrics = {}
        for param, values in all_params.items():
            if len(values) > 1:
                stability_metrics[param] = {
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'cv': np.std(values) / np.mean(values) if np.mean(values) != 0 else 0,
                    'min': np.min(values),
                    'max': np.max(values),
                    'range': np.max(values) - np.min(values)
                }
        
        return stability_metrics
    
    def generate_report(self, results: Dict, output_dir: str = 'reports') -> str:
        """
        Generate comprehensive walk-forward analysis report
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Create visualizations
        self.create_visualizations(results, output_dir)
        
        # Generate HTML report
        report_html = self.create_html_report(results)
        
        # Save report
        report_path = os.path.join(output_dir, f'walk_forward_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.html')
        with open(report_path, 'w') as f:
            f.write(report_html)
        
        return report_path
    
    def create_visualizations(self, results: Dict, output_dir: str):
        """
        Create visualization plots
        """
        # Set style
        plt.style.use('seaborn-v0_8')
        
        # 1. Equity curve
        plt.figure(figsize=(14, 8))
        plt.plot(results['combined_equity_curve'], linewidth=2, color='blue')
        plt.title('Walk-Forward Analysis - Combined Equity Curve', fontsize=16)
        plt.xlabel('Time Period')
        plt.ylabel('Equity Value')
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'equity_curve.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Drawdown analysis
        equity_array = np.array(results['combined_equity_curve'])
        running_max = np.maximum.accumulate(equity_array)
        drawdown = (equity_array - running_max) / running_max * 100
        
        plt.figure(figsize=(14, 6))
        plt.fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        plt.plot(drawdown, color='red', linewidth=1)
        plt.title('Drawdown Analysis', fontsize=16)
        plt.xlabel('Time Period')
        plt.ylabel('Drawdown (%)')
        plt.grid(True, alpha=0.3)
        plt.savefig(os.path.join(output_dir, 'drawdown.png'), dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Monte Carlo validation (if available)
        if 'monte_carlo' in results:
            mc = results['monte_carlo']
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))
            
            # Final values distribution
            axes[0, 0].hist(mc['simulated_final_values'], bins=50, alpha=0.7, color='lightblue')
            axes[0, 0].axvline(results['combined_equity_curve'][-1], color='red', linestyle='--', 
                             label=f'Actual: {results["combined_equity_curve"][-1]:.2f}')
            axes[0, 0].set_title('Final Value Distribution')
            axes[0, 0].legend()
            
            # Sharpe ratio distribution
            axes[0, 1].hist(mc['simulated_sharpe_ratios'], bins=50, alpha=0.7, color='lightgreen')
            axes[0, 1].axvline(results['combined_metrics']['sharpe_ratio'], color='red', linestyle='--',
                             label=f'Actual: {results["combined_metrics"]["sharpe_ratio"]:.2f}')
            axes[0, 1].set_title('Sharpe Ratio Distribution')
            axes[0, 1].legend()
            
            # Max drawdown distribution
            axes[1, 0].hist(mc['simulated_max_drawdowns'], bins=50, alpha=0.7, color='lightcoral')
            axes[1, 0].axvline(results['combined_metrics']['max_drawdown'], color='red', linestyle='--',
                             label=f'Actual: {results["combined_metrics"]["max_drawdown"]:.2f}')
            axes[1, 0].set_title('Max Drawdown Distribution')
            axes[1, 0].legend()
            
            # Summary statistics
            axes[1, 1].text(0.1, 0.8, f'Final Value Percentile: {mc["final_value_percentile"]:.1f}%', 
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].text(0.1, 0.6, f'Sharpe Percentile: {mc["sharpe_percentile"]:.1f}%', 
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].text(0.1, 0.4, f'Drawdown Percentile: {mc["drawdown_percentile"]:.1f}%', 
                           transform=axes[1, 1].transAxes, fontsize=12)
            axes[1, 1].set_title('Monte Carlo Summary')
            axes[1, 1].set_xticks([])
            axes[1, 1].set_yticks([])
            
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'monte_carlo.png'), dpi=300, bbox_inches='tight')
            plt.close()
        
        # 4. Parameter stability (if available)
        if 'stability_analysis' in results and results['stability_analysis']:
            stability = results['stability_analysis']
            
            param_names = list(stability.keys())
            cv_values = [stability[param]['cv'] for param in param_names]
            
            plt.figure(figsize=(12, 6))
            plt.bar(param_names, cv_values, alpha=0.7)
            plt.title('Parameter Stability (Coefficient of Variation)', fontsize=16)
            plt.xlabel('Parameters')
            plt.ylabel('Coefficient of Variation')
            plt.xticks(rotation=45)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()
            plt.savefig(os.path.join(output_dir, 'parameter_stability.png'), dpi=300, bbox_inches='tight')
            plt.close()