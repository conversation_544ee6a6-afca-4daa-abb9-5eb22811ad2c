#!/usr/bin/env python3
import os
import itertools
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone

# ── data fetch & utilities ────────────────────────────────────────────────────────
from strategies.stochastic_rsi import fetch_ohlcv
from strategies.UPDATED_rsi_macd import compute_rsi, compute_macd

def compute_atr(closes, period=14):
    tr = closes.diff().abs()
    return tr.rolling(window=period, min_periods=1).mean()

def backtest_atr(signals, init_balance, fee, slip,
                 stop_atr_mult, take_atr_mult, max_trades):
    df = signals.reset_index(drop=True).copy()
    df["atr"] = compute_atr(df["close"], period=14)

    bal, pos, entry = init_balance, 0.0, 0.0
    sl, tp = 0.0, 0.0
    trades, in_trade = [], False

    for _, row in df.iterrows():
        price, sig, atr = row["close"], row["signal"], row["atr"]
        # ENTRY
        if not in_trade and sig == "BUY" and len(trades) < max_trades:
            exec_price = price * (1 + slip)
            qty        = bal / (exec_price * (1 + fee))
            cost       = qty * exec_price
            commission = cost * fee
            if cost + commission <= bal:
                bal      -= (cost + commission)
                pos       = qty
                entry     = exec_price
                sl        = entry - stop_atr_mult * atr
                tp        = entry + take_atr_mult * atr
                in_trade  = True

        # EXIT
        elif in_trade:
            exit_price = None
            if price <= sl:
                exit_price = sl * (1 - slip)
            elif price >= tp:
                exit_price = tp * (1 - slip)
            elif sig == "SELL":
                exit_price = price * (1 - slip)

            if exit_price is not None:
                proceeds   = pos * exit_price
                commission = proceeds * fee
                pnl        = proceeds - commission - (pos * entry)
                bal       += (proceeds - commission)
                trades.append(pnl)
                pos, in_trade = 0.0, False

        if len(trades) >= max_trades:
            break

    # build equity curve
    equity = []
    for price in df["close"]:
        equity.append(bal + (pos * price if in_trade else 0))
    return equity, trades

def compute_metrics(equity, init_balance, interval):
    arr   = np.array(equity)
    rets  = arr[1:] / arr[:-1] - 1
    bars  = 1440 / int(interval[:-1])
    daily = rets * bars
    mu    = np.nanmean(daily)
    sd    = np.nanstd(daily)
    sharpe = (mu * 252) / (sd * np.sqrt(252)) if sd else np.nan
    total  = arr[-1] / init_balance - 1
    dd     = np.min((arr - np.maximum.accumulate(arr)) / np.maximum.accumulate(arr))
    return total, sharpe, dd

def trade_stats(pnls):
    if not pnls:
        return dict(n_trades=0, win_rate=np.nan, expectancy=np.nan)
    arr  = np.array(pnls)
    wins = arr[arr>0]; losses = arr[arr<0]
    wr   = len(wins) / len(arr)
    aw   = wins.mean()    if wins.size   else 0
    al   = losses.mean()  if losses.size else 0
    exp  = wr*aw + (1-wr)*al
    return dict(n_trades=len(arr), win_rate=wr, expectancy=exp)

def generate_rsi_macd_signals(df, rsi_p, macd_short, macd_long, macd_sig):
    df = df.copy()
    df["rsi"]         = compute_rsi(df["close"],   period=rsi_p)
    df["macd"], df["sig_line"] = compute_macd(
        df["close"],
        short_period=macd_short,
        long_period =macd_long,
        signal_period=macd_sig
    )
    df["signal"] = "WAIT"
    df.loc[(df["rsi"] < 30) & (df["macd"] > df["sig_line"]), "signal"] = "BUY"
    df.loc[(df["rsi"] > 70) & (df["macd"] < df["sig_line"]), "signal"] = "SELL"
    return df[["timestamp","close","signal"]]

def walk_forward_rsi_macd(
    symbol, interval, start, end,
    train_days, test_days, step_days,
    init_cap, fee, slip,
    stop_mult, take_mult, max_trades,
    rsi_periods, macd_shorts, macd_longs, macd_signals
):
    # fetch once, ensure tz-aware timestamps
    raw = fetch_ohlcv(symbol, interval, start_time=start, end_time=end)
    raw["timestamp"] = pd.to_datetime(raw["timestamp"], utc=True)

    results = []
    cursor = start

    while cursor + timedelta(days=train_days+test_days) <= end:
        train_s = cursor
        train_e = cursor + timedelta(days=train_days)
        test_s  = train_e
        test_e  = train_e + timedelta(days=test_days)
        print(f"▶️ IS {train_s.date()}→{train_e.date()}  OOS {test_s.date()}→{test_e.date()}")

        # slice
        window = raw[(raw.timestamp >= train_s) & (raw.timestamp < test_e)]
        train_raw = window[window.timestamp < train_e]
        test_raw  = window[window.timestamp >= train_e]

        # in-sample grid search
        best = {"score": -np.inf}
        for rsi_p, ms, ml, msg in itertools.product(
            rsi_periods, macd_shorts, macd_longs, macd_signals
        ):
            if ms >= ml:
                continue
            sig_is = generate_rsi_macd_signals(train_raw, rsi_p, ms, ml, msg)
            eq_is, tr_is = backtest_atr(
                sig_is, init_cap, fee, slip, stop_mult, take_mult, max_trades
            )
            tot_is, shr_is, _ = compute_metrics(eq_is, init_cap, interval)
            metric = shr_is if not np.isnan(shr_is) else tot_is
            if metric > best["score"]:
                best.update(score=metric,
                            rsi=rsi_p, short=ms, long=ml, signal=msg)

        # out-of-sample backtest
        sig_oos, _ = generate_rsi_macd_signals(
            test_raw,
            best["rsi"], best["short"], best["long"], best["signal"]
        ), None
        eq_oos, tr_oos = backtest_atr(
            sig_oos, init_cap, fee, slip, stop_mult, take_mult, max_trades
        )
        tot_oos, shr_oos, dd_oos = compute_metrics(eq_oos, init_cap, interval)
        stats = trade_stats(tr_oos)

        results.append({
            "test_start": train_e.date(),
            "test_end":   test_e.date(),
            "rsi_p":      best["rsi"],
            "macd_short": best["short"],
            "macd_long":  best["long"],
            "macd_sig":   best["signal"],
            "return":     tot_oos,
            "sharpe":     shr_oos,
            "max_dd":     dd_oos,
            "n_trades":   stats["n_trades"],
            "win_rate":   stats["win_rate"],
            "expectancy": stats["expectancy"],
        })

        cursor += timedelta(days=step_days)

    return pd.DataFrame(results)

if __name__ == "__main__":
    symbol, interval = "BTCUSDT", "15m"
    start = datetime(2023,1,1, tzinfo=timezone.utc)
    end   = datetime(2023,7,1, tzinfo=timezone.utc)

    df = walk_forward_rsi_macd(
        symbol, interval, start, end,
        train_days   =120,
        test_days    =14,
        step_days    =7,
        init_cap     =1000.0,
        fee          =0.001,
        slip         =0.001,
        stop_mult    =1.0,
        take_mult    =2.0,
        max_trades   =100,
        rsi_periods  =[10,14,20],
        macd_shorts  =[8,12],
        macd_longs   =[17,26],
        macd_signals =[5,9]
    )

    print("\n--- RSI+MACD Walk‑Forward Results ---")
    print(df.to_string(index=False))

    os.makedirs("walk_forward_results", exist_ok=True)
    df.to_csv("walk_forward_results/oos_rsi_macd.csv", index=False)
