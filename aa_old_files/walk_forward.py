#!/usr/bin/env python3
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone

# ── strategy imports ─────────────────────────────────────────────────────────────
from strategies.UPDATE<PERSON>_bollinger     import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd      import fetch_rsi_macd
from strategies.supertrend            import fetch_supertrend
from strategies.stochastic_rsi        import fetch_stoch_rsi

# ── constants & helpers ─────────────────────────────────────────────────────────
SIGNAL_SCORE = {"BUY": 1, "WAIT": 0, "SELL": -1}

def compute_atr(closes, period=14):
    """Approximate AT<PERSON> via close-to-close absolute moves."""
    tr = closes.diff().abs()
    return tr.rolling(window=period, min_periods=1).mean()

def merge_and_signal(dfs, weights, threshold):
    """
    Merge the five strategy DataFrames and compute a weighted vote.
    Each df must have ['timestamp','signal','close'].
    """
    df = None
    # 1) merge signals & individual closes
    for name, strat in dfs.items():
        tmp = strat[["timestamp","signal","close"]].rename(
            columns={"signal":f"signal_{name}", "close":f"close_{name}"})
        tmp[f"score_{name}"] = tmp[f"signal_{name}"].map(SIGNAL_SCORE)
        df = tmp if df is None else df.merge(tmp, on="timestamp", how="outer")

    # 2) fill gaps & pick a single close (bollinger)
    df = df.sort_values("timestamp").ffill().bfill()
    df["close"] = df["close_bollinger"]

    # 3) compute weighted_score
    df["weighted_score"] = sum(df[f"score_{n}"] * w for n, w in weights.items())
    df["final_signal"] = df["weighted_score"].apply(
        lambda x: "BUY"  if x >  threshold
                  else "SELL" if x < -threshold
                  else "WAIT"
    )

    return df[["timestamp","close","final_signal"]]

def fetch_all(symbol, interval, start, end):
    return {
      "bollinger":      fetch_bollinger(   symbol=symbol, interval=interval, start_time=start, end_time=end),
      "ema_crossover":  fetch_ema_crossover(symbol=symbol, interval=interval, start_time=start, end_time=end),
      "rsi_macd":       fetch_rsi_macd(     symbol=symbol, interval=interval, start_time=start, end_time=end),
      "supertrend":     fetch_supertrend(   symbol=symbol, interval=interval, start_time=start, end_time=end),
      "stochastic_rsi": fetch_stoch_rsi(    symbol=symbol, interval=interval, start_time=start, end_time=end),
    }

def backtest(signals, init_balance, fee, slip,
             stop_atr_mult, take_atr_mult, max_trades):
    """
    ATR‑based stop‑loss & take‑profit backtest.
    `signals` must have ['timestamp','close','final_signal'].
    """
    df = signals.reset_index(drop=True)
    df["atr"] = compute_atr(df["close"], period=14)

    bal, pos, entry = init_balance, 0.0, 0.0
    sl, tp = 0.0, 0.0
    trades, in_trade = [], False

    for _, row in df.iterrows():
        price, sig, atr = row["close"], row["final_signal"], row["atr"]

        # ENTRY
        if not in_trade and sig == "BUY" and len(trades) < max_trades:
            exec_price = price * (1 + slip)
            qty        = bal / (exec_price * (1 + fee))
            cost       = qty * exec_price
            commission = cost * fee
            if cost + commission <= bal:
                bal      -= (cost + commission)
                pos       = qty
                entry     = exec_price
                sl        = entry - stop_atr_mult * atr
                tp        = entry + take_atr_mult * atr
                in_trade  = True

        # EXIT
        elif in_trade:
            exit_price = None

            if price <= sl:
                exit_price = sl * (1 - slip)
            elif price >= tp:
                exit_price = tp * (1 - slip)
            elif sig == "SELL":
                exit_price = price * (1 - slip)

            if exit_price is not None:
                proceeds   = pos * exit_price
                commission = proceeds * fee
                pnl        = proceeds - commission - (pos * entry)
                bal       += (proceeds - commission)
                trades.append({"entry": entry, "exit": exit_price, "pnl": pnl})
                pos, in_trade = 0.0, False

        if len(trades) >= max_trades:
            break

    # equity curve
    equity = []
    for c in df["close"]:
        equity.append(bal + (pos * c if in_trade else 0))
    return equity, trades

def compute_metrics(equity, init, interval):
    arr   = np.array(equity)
    rets  = arr[1:] / arr[:-1] - 1
    bars  = 1440 / int(interval[:-1])  # e.g. "15m"
    daily = rets * bars
    mu, sd = np.nanmean(daily), np.nanstd(daily)
    sharpe = (mu * 252) / (sd * np.sqrt(252)) if sd else np.nan
    total  = arr[-1] / init - 1
    dd     = np.min((arr - np.maximum.accumulate(arr)) / np.maximum.accumulate(arr))
    return total, sharpe, dd

def trade_stats(trades):
    if not trades:
        return dict(n_trades=0, win_rate=np.nan,
                    avg_win=np.nan, avg_loss=np.nan,
                    expectancy=np.nan)
    pnls     = np.array([t["pnl"] for t in trades])
    wins     = pnls[pnls>0]; losses = pnls[pnls<0]
    wr       = len(wins)/len(pnls)
    aw       = wins.mean()  if wins.size   else 0
    al       = losses.mean() if losses.size else 0
    expectancy = wr*aw + (1-wr)*al
    return dict(n_trades=len(pnls), win_rate=wr,
                avg_win=aw, avg_loss=al, expectancy=expectancy)

def walk_forward(symbol, interval, start, end,
                 train_days, test_days, step_days,
                 init_cap, fee, slip,
                 stop_atr_mult, take_atr_mult,
                 thresholds, max_trades,
                 variations):
    """
    variations: dict mapping name→weights dict.
    Returns a DataFrame with one row per (window,variation).
    """
    out = []
    cursor = start

    while cursor + timedelta(days=train_days+test_days) <= end:
        train_s, train_e = cursor, cursor + timedelta(days=train_days)
        test_s,  test_e  = train_e,  train_e + timedelta(days=test_days)
        print(f"▶️ IS {train_s.date()}→{train_e.date()}  OOS {test_s.date()}→{test_e.date()}")

        # fetch all five strategy signals once per window
        train_dfs = fetch_all(symbol, interval, train_s, train_e)
        test_dfs  = fetch_all(symbol, interval, test_s,  test_e)

        # loop through each variation
        for name, weights in variations.items():
            # 1) in‑sample threshold optimization
            best_score, best_thr = -np.inf, thresholds[0]
            for thr in thresholds:
                sig_is, _ = merge_and_signal(train_dfs, weights, thr), None
                eq_is, tr_is = backtest(sig_is, init_cap, fee, slip,
                                        stop_atr_mult, take_atr_mult, max_trades)
                tot, shr, _ = compute_metrics(eq_is, init_cap, interval)
                metric = shr if not np.isnan(shr) else tot
                if metric > best_score:
                    best_score, best_thr = metric, thr

            # 2) out‑of‑sample test with best_thr
            sig_oos, _ = merge_and_signal(test_dfs, weights, best_thr), None
            eq_oos, tr_oos = backtest(sig_oos, init_cap, fee, slip,
                                      stop_atr_mult, take_atr_mult, max_trades)
            tot_oos, shr_oos, dd_oos = compute_metrics(eq_oos, init_cap, interval)
            stats = trade_stats(tr_oos)

            out.append({
                "variation":      name,
                "test_start":     test_s.date(),
                "test_end":       test_e.date(),
                "return":         tot_oos,
                "sharpe":         shr_oos,
                "max_drawdown":   dd_oos,
                "n_trades":       stats["n_trades"],
                "win_rate":       stats["win_rate"],
                "expectancy":     stats["expectancy"],
                "threshold":      best_thr,
                **{f"w_{k}": v for k, v in weights.items()}
            })

        cursor += timedelta(days=step_days)

    return pd.DataFrame(out)

if __name__ == "__main__":
    # ── parameters ───────────────────────────────────────────────────────────────
    symbol   = "BTCUSDT"
    interval = "15m"
    start    = datetime(2023,1,1, tzinfo=timezone.utc)
    end      = datetime(2023,7,1, tzinfo=timezone.utc)

    train_days, test_days, step_days = 120, 14, 7
    init_cap, fee, slip            = 1000.0, 0.001, 0.001
    stop_atr_mult, take_atr_mult   = 1.0, 2.0
    max_trades                     = 24
    thresholds                     = np.arange(0.20, 0.46, 0.05).tolist()

    # ── five variations (all‑5 + four 3‑indicator mixes) ──────────────────────────
    variations = {
        "all_5": {
            "bollinger":      0.25,
            "ema_crossover":  0.15,
            "rsi_macd":       0.25,
            "stochastic_rsi": 0.20,
            "supertrend":     0.15
        },
        "trend_mix": {
            "bollinger": 0.00, "ema_crossover": 1/3, "rsi_macd": 1/3,
            "stochastic_rsi": 0.00, "supertrend": 1/3
        },
        "mean_rev_mix": {
            "bollinger": 1/3, "ema_crossover": 0.00, "rsi_macd": 0.00,
            "stochastic_rsi": 1/3, "supertrend": 1/3
        },
        "momentum_mix": {
            "bollinger": 0.00, "ema_crossover": 1/3, "rsi_macd": 0.00,
            "stochastic_rsi": 1/3, "supertrend": 1/3
        },
        "breakout_mix": {
            "bollinger": 1/3, "ema_crossover": 0.00, "rsi_macd": 1/3,
            "stochastic_rsi": 0.00, "supertrend": 1/3
        },
    }

    # ── run walk‑forward for all variations ────────────────────────────────────────
    results = walk_forward(
        symbol, interval, start, end,
        train_days, test_days, step_days,
        init_cap, fee, slip,
        stop_atr_mult, take_atr_mult,
        thresholds, max_trades,
        variations
    )

    print("\n--- OOS METRICS ---")
    print(results.to_string(index=False))

    os.makedirs("walk_forward_results", exist_ok=True)
    results.to_csv("walk_forward_results/oos_metrics.csv", index=False)
