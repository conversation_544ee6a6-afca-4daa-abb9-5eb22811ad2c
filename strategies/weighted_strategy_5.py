import sys
import os
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
import pandas as pd
from datetime import datetime
from matplotlib import pyplot as plt
from strategies.UPDATED_bollinger import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd import fetch_rsi_macd
from strategies.stochastic_rsi import fetch_stoch_rsi
from strategies.supertrend import fetch_supertrend

# A<PERSON><PERSON><PERSON><PERSON>klar (değiştirilebilir)
STRATEGY_WEIGHTS = {
    "bollinger": 0.25,
    "ema_crossover": 0.15,
    "rsi_macd": 0.25,
    "stochastic_rsi": 0.20,
    "supertrend": 0.15,
}

# <PERSON>yal skorları
SIGNAL_SCORE = {"BUY": 1, "WAIT": 0, "SELL": -1}


def merge_signals(signals: dict) -> pd.DataFrame:
    df = None
    for name, strat_df in signals.items():
        strat_df = strat_df[["timestamp", "signal"]].copy()
        strat_df.rename(columns={"signal": f"signal_{name}"}, inplace=True)

        # Skor sütunu ekle (BUY = 1, SELL = -1, WAIT = 0)
        strat_df[f"score_{name}"] = strat_df[f"signal_{name}"].map(SIGNAL_SCORE)

        if df is None:
            df = strat_df
        else:
            df = pd.merge(df, strat_df, on="timestamp", how="outer")

    df.sort_values("timestamp", inplace=True)
    df.ffill(inplace=True)
    df.bfill(inplace=True)

    # Eksik skor kolonları varsa 0 ile ekle
    for name in STRATEGY_WEIGHTS:
        col_name = f"score_{name}"
        if col_name not in df.columns:
            print(f"⚠️ Uyarı: '{col_name}' bulunamadı, 0 ile doldurulacak.")
            df[col_name] = 0

    # Skorları ağırlıklı hesapla
    score_cols = [f"score_{name}" for name in STRATEGY_WEIGHTS]
    df["weighted_score"] = sum(df[col] * STRATEGY_WEIGHTS[name] for name, col in zip(STRATEGY_WEIGHTS, score_cols))

    # Son sinyali belirle
    df["final_signal"] = df["weighted_score"].apply(lambda x: "BUY" if x > 0.2 else "SELL" if x < -0.2 else "WAIT")

    print("🧪 Mevcut sütunlar:", df.columns.tolist())
    print("✅ Final sinyal dağılımı:")
    print(df["final_signal"].value_counts())
    print("✅ Weighted score min/max:", df["weighted_score"].min(), "/", df["weighted_score"].max())

    return df



def fetch_all_signals(symbol, interval, start_time, end_time):
    return {
        "bollinger": fetch_bollinger(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time),
        "ema_crossover": fetch_ema_crossover(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time),  # 🔥 düzeltildi
        "rsi_macd": fetch_rsi_macd(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time),
        "supertrend": fetch_supertrend(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time),
        "stochastic_rsi": fetch_stoch_rsi(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time),  # 🔥 düzeltildi
    }



def backtest(df: pd.DataFrame, initial_balance=1000.0,
             fee_rate=0.001, slippage_rate=0.0005):
    balance = initial_balance
    position = 0.0
    portfolio = []

    for i, row in df.iterrows():
        signal = row["final_signal"]
        price = row["close"]

        if signal == "BUY" and position == 0.0:
            execution_price = price * (1 + slippage_rate)
            quantity = balance / (execution_price * (1 + fee_rate))  # Fee dahil
            cost = quantity * execution_price
            fee = cost * fee_rate
            total_cost = cost + fee

            if balance >= total_cost:
                position = quantity
                balance -= total_cost

        elif signal == "SELL" and position > 0.0:
            execution_price = price * (1 - slippage_rate)
            proceeds = position * execution_price
            fee = proceeds * fee_rate
            balance += proceeds - fee
            position = 0.0

        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    final_value = balance + (position * df.iloc[-1]["close"])
    return portfolio, final_value




def run_weighted_strategy(symbol="BTCUSDT", interval="15m", start_time=None, end_time=None):
    signals = fetch_all_signals(symbol, interval, start_time, end_time)
    final_df = merge_signals(signals)
    final_df["close"] = signals["bollinger"]["close"]  # Referans için close fiyatı
    print("✅ Close sütunu boş veri sayısı:", final_df["close"].isna().sum())


    portfolio, final_value = backtest(final_df, fee_rate=0.001, slippage_rate=0.0005)
    print(f"💼 Final Portfolio Value: ${final_value:.2f}")

    # Grafik çizimi
    os.makedirs("backtest_results/plots", exist_ok=True)
    plt.figure(figsize=(10, 4))
    plt.plot(final_df["timestamp"], portfolio, label="Portfolio Value")
    plt.title("📈 Weighted Strategy Portfolio Performance")
    plt.xlabel("Time")
    plt.ylabel("Portfolio Value ($)")
    plt.legend()
    plt.grid(True)
    plt.tight_layout()
    plt.savefig("backtest_results/plots/weighted_strategy_portfolio.png")
    plt.show()


if __name__ == "__main__":
    run_weighted_strategy(
        start_time=datetime(2023, 1, 1),
        end_time=datetime(2023, 4, 1)
    )
