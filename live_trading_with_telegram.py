#!/usr/bin/env python3
"""
Live Trading System with Telegram Notifications
Uses the optimized ETHUSDT 1h strategy with mock trading and real-time notifications
"""

import os
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from binance.client import Client
from dotenv import load_dotenv
import logging
import requests
from typing import Dict, List, Optional
import json

# Import our enhanced components
from enhanced_strategy_system import EnhancedStrategySystem
from risk_management import AdvancedRiskManager, PortfolioManager

# Load environment variables
load_dotenv()

class TelegramNotifier:
    """
    Telegram notification system for trading alerts
    """
    
    def __init__(self):
        self.bot_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.chat_id = os.getenv("TELEGRAM_CHAT_ID")
        
        if not self.bot_token or not self.chat_id:
            print("⚠️ Warning: Telegram credentials not found in .env file")
            print("\n📱 TO ENABLE TELEGRAM NOTIFICATIONS:")
            print("1. Create a bot: Message @BotFather on Telegram")
            print("2. Send /newbot and follow instructions")
            print("3. Copy the bot token")
            print("4. Get your chat ID: Message @userinfobot")
            print("5. Add to .env file:")
            print("   TELEGRAM_BOT_TOKEN=your_bot_token_here")
            print("   TELEGRAM_CHAT_ID=your_chat_id_here")
            print("\nFor now, notifications will be printed to console.")
            self.enabled = False
        else:
            self.enabled = True
            print("✅ Telegram notifications enabled")
    
    def send_message(self, message: str, parse_mode: str = "HTML"):
        """
        Send message to Telegram
        """
        if not self.enabled:
            print(f"📱 [TELEGRAM]: {message}")
            return False
        
        try:
            url = f"https://api.telegram.org/bot{self.bot_token}/sendMessage"
            data = {
                "chat_id": self.chat_id,
                "text": message,
                "parse_mode": parse_mode
            }
            
            response = requests.post(url, data=data, timeout=10)
            return response.status_code == 200
            
        except Exception as e:
            print(f"❌ Telegram error: {e}")
            return False
    
    def send_trade_alert(self, action: str, symbol: str, price: float, 
                        quantity: float, signal_strength: float, market_condition: str):
        """
        Send trade execution alert
        """
        emoji = "🟢" if action == "BUY" else "🔴"
        
        message = f"""
{emoji} <b>TRADE EXECUTED</b>

<b>Action:</b> {action}
<b>Symbol:</b> {symbol}
<b>Price:</b> ${price:,.2f}
<b>Quantity:</b> {quantity:.6f}
<b>Value:</b> ${price * quantity:,.2f}

<b>Signal Strength:</b> {signal_strength:.2f}
<b>Market Condition:</b> {market_condition}
<b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return self.send_message(message.strip())
    
    def send_position_closed(self, symbol: str, entry_price: float, exit_price: float,
                           quantity: float, pnl: float, reason: str, hold_time: str):
        """
        Send position closure alert
        """
        emoji = "💚" if pnl > 0 else "❤️"
        pnl_pct = (pnl / (entry_price * quantity)) * 100 if entry_price * quantity > 0 else 0
        
        message = f"""
{emoji} <b>POSITION CLOSED</b>

<b>Symbol:</b> {symbol}
<b>Entry Price:</b> ${entry_price:,.2f}
<b>Exit Price:</b> ${exit_price:,.2f}
<b>Quantity:</b> {quantity:.6f}

<b>P&L:</b> ${pnl:,.2f} ({pnl_pct:+.2f}%)
<b>Reason:</b> {reason}
<b>Hold Time:</b> {hold_time}
<b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return self.send_message(message.strip())
    
    def send_status_update(self, balance: float, total_pnl: float, active_positions: int,
                          total_trades: int, win_rate: float, daily_pnl: float):
        """
        Send hourly status update
        """
        emoji = "📈" if daily_pnl >= 0 else "📉"
        
        message = f"""
{emoji} <b>TRADING STATUS UPDATE</b>

<b>Current Balance:</b> ${balance:,.2f}
<b>Total P&L:</b> ${total_pnl:,.2f}
<b>Daily P&L:</b> ${daily_pnl:,.2f}

<b>Active Positions:</b> {active_positions}
<b>Total Trades:</b> {total_trades}
<b>Win Rate:</b> {win_rate:.1%}

<b>Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        return self.send_message(message.strip())

class MockTradingBot:
    """
    Mock trading bot with real market data and Telegram notifications
    """
    
    def __init__(self, 
                 initial_capital: float = 1000,
                 risk_per_trade: float = 0.01,
                 max_positions: int = 2,
                 symbol: str = 'ETHUSDT',
                 interval: str = '1h'):
        
        # Initialize Binance client for market data
        self.api_key = os.getenv("BINANCE_API_KEY")
        self.secret_key = os.getenv("BINANCE_SECRET_KEY")
        
        if not self.api_key or not self.secret_key:
            raise ValueError("Please set BINANCE_API_KEY and BINANCE_SECRET_KEY in your .env file")
        
        self.client = Client(self.api_key, self.secret_key)
        
        # Trading parameters (optimized settings)
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_positions = max_positions
        self.symbol = symbol
        self.interval = interval
        
        # Initialize components
        self.strategy_system = EnhancedStrategySystem()
        self.telegram = TelegramNotifier()
        
        # Mock portfolio tracking
        self.current_balance = initial_capital
        self.positions = {}
        self.trade_history = []
        self.daily_start_balance = initial_capital
        
        # Setup logging
        self.setup_logging()
        
        # Trading state
        self.is_running = False
        self.last_status_update = None
        self.last_signal_check = None
        
        # Send startup notification
        self.telegram.send_message(f"""
🚀 <b>TRADING BOT STARTED</b>

<b>Mode:</b> MOCK TRADING (Paper Trading)
<b>Symbol:</b> {symbol}
<b>Timeframe:</b> {interval}
<b>Initial Capital:</b> ${initial_capital:,.2f}
<b>Risk per Trade:</b> {risk_per_trade:.1%}
<b>Max Positions:</b> {max_positions}

Bot is now monitoring the market...
        """.strip())
    
    def setup_logging(self):
        """Setup logging for the trading bot"""
        log_filename = f"mock_trading_{datetime.now().strftime('%Y%m%d')}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_current_price(self, symbol: str) -> float:
        """Get current market price"""
        try:
            ticker = self.client.get_symbol_ticker(symbol=symbol)
            return float(ticker['price'])
        except Exception as e:
            self.logger.error(f"Error getting price for {symbol}: {e}")
            return 0.0
    
    def check_signals(self) -> Dict:
        """Check for trading signals using optimized strategy"""
        try:
            # Get recent data for signal generation (last 24 hours)
            end_time = datetime.now(timezone.utc)
            start_time = end_time - timedelta(hours=48)  # 48 hours for 1h timeframe
            
            # Generate signals using the optimized ETHUSDT 1h strategy
            signals_df = self.strategy_system.create_adaptive_ensemble(
                self.symbol, self.interval, start_time, end_time
            )
            
            if len(signals_df) == 0:
                return {'signal': 'WAIT', 'strength': 0, 'price': 0}
            
            # Get latest signal
            latest = signals_df.iloc[-1]
            current_price = self.get_current_price(self.symbol)
            
            return {
                'signal': latest['signal'],
                'strength': latest.get('signal_strength', 0.5),
                'price': current_price,
                'market_condition': latest.get('market_condition', 'unknown'),
                'ensemble_score': latest.get('ensemble_score', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error checking signals: {e}")
            return {'signal': 'WAIT', 'strength': 0, 'price': 0}
    
    def calculate_position_size(self, signal_strength: float, current_price: float) -> float:
        """Calculate position size for the trade"""
        risk_amount = self.current_balance * self.risk_per_trade
        
        # Adjust for signal strength
        adjusted_risk = risk_amount * signal_strength
        
        # Calculate position size in base currency
        position_size = adjusted_risk / current_price
        
        # Minimum position size (equivalent to $10)
        min_position = 10 / current_price
        
        return max(position_size, min_position)
    
    def execute_mock_trade(self, signal_data: Dict) -> bool:
        """Execute a mock trade"""
        signal = signal_data['signal']
        strength = signal_data['strength']
        price = signal_data['price']
        market_condition = signal_data.get('market_condition', 'unknown')
        
        if signal == 'WAIT':
            return False
        
        # Check if we can open new position
        if len(self.positions) >= self.max_positions:
            self.logger.info("Maximum positions reached, skipping trade")
            return False
        
        # Calculate position size
        position_size = self.calculate_position_size(strength, price)
        trade_value = position_size * price
        
        # Check if we have enough balance
        if trade_value > self.current_balance:
            self.logger.info("Insufficient balance for trade")
            return False
        
        # Execute mock trade
        self.current_balance -= trade_value
        
        # Record the position
        position_id = f"{self.symbol}_{int(time.time())}"
        self.positions[position_id] = {
            'symbol': self.symbol,
            'side': signal,
            'quantity': position_size,
            'entry_price': price,
            'entry_time': datetime.now(),
            'signal_strength': strength,
            'market_condition': market_condition
        }
        
        # Record trade
        trade_record = {
            'timestamp': datetime.now(),
            'type': 'ENTRY',
            'symbol': self.symbol,
            'side': signal,
            'quantity': position_size,
            'price': price,
            'signal_strength': strength,
            'market_condition': market_condition
        }
        
        self.trade_history.append(trade_record)
        
        # Send Telegram notification
        self.telegram.send_trade_alert(
            signal, self.symbol, price, position_size, strength, market_condition
        )
        
        self.logger.info(f"✅ Mock {signal} executed: {position_size:.6f} {self.symbol} at ${price:.2f}")
        
        return True
    
    def check_exits(self) -> bool:
        """Check if we should exit any positions"""
        current_price = self.get_current_price(self.symbol)
        
        for position_id, position in list(self.positions.items()):
            entry_price = position['entry_price']
            quantity = position['quantity']
            
            # Simple exit logic (enhanced from original)
            if position['side'] == 'BUY':
                profit_pct = (current_price - entry_price) / entry_price
                
                # Dynamic exit based on signal strength
                take_profit_pct = 0.03 + (position['signal_strength'] * 0.02)  # 3-5% profit
                stop_loss_pct = -0.015 - (position['signal_strength'] * 0.005)  # 1.5-2% loss
                
                if profit_pct >= take_profit_pct:
                    self.close_position(position_id, current_price, 'TAKE_PROFIT')
                    return True
                elif profit_pct <= stop_loss_pct:
                    self.close_position(position_id, current_price, 'STOP_LOSS')
                    return True
        
        return False
    
    def close_position(self, position_id: str, exit_price: float, reason: str):
        """Close a mock position"""
        if position_id not in self.positions:
            return
        
        position = self.positions[position_id]
        
        # Calculate P&L
        if position['side'] == 'BUY':
            pnl = (exit_price - position['entry_price']) * position['quantity']
        else:
            pnl = (position['entry_price'] - exit_price) * position['quantity']
        
        # Update balance
        proceeds = position['quantity'] * exit_price
        self.current_balance += proceeds
        
        # Calculate hold time
        hold_time = datetime.now() - position['entry_time']
        hold_time_str = str(hold_time).split('.')[0]  # Remove microseconds
        
        # Record exit
        exit_record = {
            'timestamp': datetime.now(),
            'type': 'EXIT',
            'symbol': position['symbol'],
            'side': 'SELL' if position['side'] == 'BUY' else 'BUY',
            'quantity': position['quantity'],
            'price': exit_price,
            'pnl': pnl,
            'reason': reason,
            'hold_time': hold_time
        }
        
        self.trade_history.append(exit_record)
        
        # Send Telegram notification
        self.telegram.send_position_closed(
            position['symbol'], position['entry_price'], exit_price,
            position['quantity'], pnl, reason, hold_time_str
        )
        
        # Remove position
        del self.positions[position_id]
        
        self.logger.info(f"🔄 Position closed: {position['symbol']} at ${exit_price:.2f}")
        self.logger.info(f"P&L: ${pnl:.2f}, Reason: {reason}")
    
    def get_performance_summary(self) -> Dict:
        """Get trading performance summary"""
        if not self.trade_history:
            return {
                'total_trades': 0, 'total_pnl': 0, 'win_rate': 0,
                'current_balance': self.current_balance, 'daily_pnl': 0,
                'active_positions': len(self.positions)
            }
        
        # Calculate metrics
        exit_trades = [t for t in self.trade_history if t['type'] == 'EXIT']
        
        if not exit_trades:
            daily_pnl = self.current_balance - self.daily_start_balance
            return {
                'total_trades': len([t for t in self.trade_history if t['type'] == 'ENTRY']),
                'total_pnl': self.current_balance - self.initial_capital,
                'win_rate': 0,
                'current_balance': self.current_balance,
                'daily_pnl': daily_pnl,
                'active_positions': len(self.positions)
            }
        
        total_pnl = sum(t['pnl'] for t in exit_trades)
        winning_trades = [t for t in exit_trades if t['pnl'] > 0]
        win_rate = len(winning_trades) / len(exit_trades) if exit_trades else 0
        
        # Calculate daily P&L
        daily_pnl = self.current_balance - self.daily_start_balance
        
        return {
            'total_trades': len(exit_trades),
            'total_pnl': total_pnl,
            'win_rate': win_rate,
            'current_balance': self.current_balance,
            'daily_pnl': daily_pnl,
            'active_positions': len(self.positions)
        }
    
    def send_hourly_status(self):
        """Send hourly status update"""
        performance = self.get_performance_summary()
        
        self.telegram.send_status_update(
            balance=performance['current_balance'],
            total_pnl=performance['total_pnl'],
            active_positions=performance['active_positions'],
            total_trades=performance['total_trades'],
            win_rate=performance['win_rate'],
            daily_pnl=performance['daily_pnl']
        )
    
    def run_trading_loop(self, check_interval: int = 1800):  # 30 minutes
        """Main trading loop"""
        self.logger.info(f"🚀 Starting mock trading bot...")
        self.logger.info(f"Symbol: {self.symbol}, Timeframe: {self.interval}")
        self.logger.info(f"Check interval: {check_interval}s")
        
        self.is_running = True
        
        try:
            while self.is_running:
                current_time = datetime.now()
                
                # Check for exit signals first
                self.check_exits()
                
                # Check for new entry signals
                signal_data = self.check_signals()
                
                if signal_data['signal'] != 'WAIT':
                    self.logger.info(f"📊 Signal detected: {signal_data['signal']} "
                                   f"(strength: {signal_data['strength']:.2f}, "
                                   f"condition: {signal_data.get('market_condition', 'unknown')})")
                    
                    self.execute_mock_trade(signal_data)
                
                # Send hourly status update
                if not self.last_status_update or \
                   (current_time - self.last_status_update).seconds >= 3600:
                    
                    self.send_hourly_status()
                    self.last_status_update = current_time
                
                # Reset daily balance at midnight
                if current_time.hour == 0 and current_time.minute < 30:
                    self.daily_start_balance = self.current_balance
                
                # Wait before next check
                self.logger.info(f"💤 Waiting {check_interval}s until next check...")
                time.sleep(check_interval)
                
        except KeyboardInterrupt:
            self.logger.info("🛑 Trading bot stopped by user")
            self.stop_trading()
        except Exception as e:
            self.logger.error(f"❌ Error in trading loop: {e}")
            self.telegram.send_message(f"❌ <b>BOT ERROR</b>\n\n{str(e)}")
            self.stop_trading()
    
    def stop_trading(self):
        """Stop the trading bot"""
        self.is_running = False
        
        # Close all positions
        current_price = self.get_current_price(self.symbol)
        for position_id in list(self.positions.keys()):
            self.close_position(position_id, current_price, 'BOT_SHUTDOWN')
        
        # Final performance report
        performance = self.get_performance_summary()
        
        final_message = f"""
🏁 <b>TRADING BOT STOPPED</b>

<b>Final Performance:</b>
• Total Trades: {performance['total_trades']}
• Total P&L: ${performance['total_pnl']:.2f}
• Win Rate: {performance['win_rate']:.1%}
• Final Balance: ${performance['current_balance']:.2f}
• Total Return: {(performance['current_balance'] - self.initial_capital) / self.initial_capital:.2%}

Bot has been safely shut down.
        """
        
        self.telegram.send_message(final_message.strip())
        self.logger.info("🏁 Trading bot stopped successfully")

def main():
    """
    Main function to start the optimized live trading system
    """
    print("🚀 OPTIMIZED LIVE TRADING SYSTEM WITH TELEGRAM")
    print("=" * 60)
    print("Using optimized settings: ETHUSDT 1h timeframe")
    print("Mode: MOCK TRADING (Paper Trading with Real Data)")
    
    # Initialize bot with optimized settings
    bot = MockTradingBot(
        initial_capital=1000,      # Start with $1000
        risk_per_trade=0.01,       # 1% risk per trade
        max_positions=2,           # Max 2 positions
        symbol='ETHUSDT',          # Optimized symbol
        interval='1h'              # Optimized timeframe
    )
    
    # Start trading
    try:
        bot.run_trading_loop(
            check_interval=1800    # Check every 30 minutes (for 1h timeframe)
        )
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
