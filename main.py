# main.py

from binance.client import Client
from config.env import BINANCE_API_KEY, BINANCE_SECRET_KEY
from config.env import TELEGRAM_BOT_TOKEN, TELEGRAM_CHAT_ID

from utils.telegram_notify import send_telegram_message
from strategies.rsi_macd import generate_signal

import datetime

def get_btc_price():
    client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)
    ticker = client.get_symbol_ticker(symbol="BTCUSDT")
    return float(ticker["price"])

if __name__ == "__main__":
    # Fiyatı al
    price = get_btc_price()
    price_msg = f"📊 Anlık BTC/USDT fiyatı: ${price:.2f}"

    # Strateji sinyali al
    signal = generate_signal()
    signal_msg = f"📈 RSI + MACD sinyali: {signal}"

    # Zaman bilgisi
    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    time_msg = f"🕒 {now}"

    # Konsola yaz
    print(price_msg)
    print(signal_msg)
    print(time_msg)

    # Telegram'a gönder
    full_msg = f"{price_msg}\n{signal_msg}\n{time_msg}"
    send_telegram_message(full_msg)

