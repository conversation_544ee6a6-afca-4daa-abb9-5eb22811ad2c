#!/usr/bin/env python3
"""
Ultimate Crypto Trading System
Integrates all improvements: enhanced strategies, risk management, and optimization
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Import our enhanced components
from enhanced_backtester import EnhancedBacktester
from risk_management import AdvancedRiskManager, PortfolioManager
from strategy_optimizer import StrategyOptimizer

# Import strategies
try:
    from strategies.UPDATED_rsi_macd import fetch_rsi_macd
    from strategies.UPDATED_ema_crossover import fetch_ema_crossover
    from strategies.UPDATED_bollinger import fetch_bollinger
    from strategies.supertrend import fetch_supertrend
    from strategies.stochastic_rsi import fetch_stoch_rsi
    from strategies.advanced_momentum_strategy import fetch_advanced_momentum
except ImportError as e:
    print(f"Warning: Some strategies not available: {e}")

class UltimateTradingSystem:
    """
    Ultimate trading system combining all enhancements
    """
    
    def __init__(self, initial_capital: float = 10000, 
                 risk_per_trade: float = 0.015,
                 max_positions: int = 3):
        
        self.initial_capital = initial_capital
        self.risk_per_trade = risk_per_trade
        self.max_positions = max_positions
        
        # Initialize components
        self.risk_manager = AdvancedRiskManager(
            initial_capital=initial_capital,
            max_risk_per_trade=risk_per_trade
        )
        self.portfolio_manager = PortfolioManager(max_positions=max_positions)
        self.optimizer = StrategyOptimizer(initial_capital)
        
        # Strategy configurations
        self.strategy_configs = {
            'conservative': {
                'rsi_macd': 0.30,
                'bollinger': 0.35,
                'stochastic_rsi': 0.35
            },
            'aggressive': {
                'advanced_momentum': 0.40,
                'ema_crossover': 0.30,
                'supertrend': 0.30
            },
            'balanced': {
                'rsi_macd': 0.20,
                'ema_crossover': 0.20,
                'bollinger': 0.20,
                'advanced_momentum': 0.25,
                'supertrend': 0.15
            }
        }
        
        # Market regime thresholds (more aggressive for crypto)
        self.regime_thresholds = {
            'trending': 0.15,   # Lower threshold for trending markets
            'ranging': 0.20,    # Lower threshold for ranging markets
            'volatile': 0.25    # Lower threshold for volatile markets
        }
    
    def detect_market_regime(self, df: pd.DataFrame) -> str:
        """
        Detect current market regime
        """
        if len(df) < 50:
            return 'ranging'
        
        # Calculate trend strength
        ema_20 = df['close'].ewm(span=20).mean()
        ema_50 = df['close'].ewm(span=50).mean()
        trend_strength = abs(ema_20.iloc[-1] - ema_50.iloc[-1]) / ema_50.iloc[-1]
        
        # Calculate volatility
        returns = df['close'].pct_change().dropna()
        volatility = returns.rolling(20).std().iloc[-1]
        volatility_percentile = (volatility > returns.rolling(50).std()).sum() / 50
        
        # Classify regime
        if volatility_percentile > 0.8:
            return 'volatile'
        elif trend_strength > 0.02:
            return 'trending'
        else:
            return 'ranging'
    
    def generate_ensemble_signals(self, symbol: str, interval: str,
                                 start_time: datetime, end_time: datetime,
                                 config_name: str = 'balanced') -> pd.DataFrame:
        """
        Generate ensemble signals from multiple strategies
        """
        print(f"🔄 Generating ensemble signals for {symbol}...")
        
        config = self.strategy_configs.get(config_name, self.strategy_configs['balanced'])
        strategy_data = {}
        
        # Fetch data from each strategy
        for strategy_name, weight in config.items():
            try:
                if strategy_name == 'rsi_macd':
                    df = fetch_rsi_macd(symbol, interval, start_time=start_time, end_time=end_time)
                elif strategy_name == 'ema_crossover':
                    df = fetch_ema_crossover(symbol, interval, start_time=start_time, end_time=end_time)
                elif strategy_name == 'bollinger':
                    df = fetch_bollinger(symbol, interval, start_time=start_time, end_time=end_time)
                elif strategy_name == 'supertrend':
                    df = fetch_supertrend(symbol, interval, start_time=start_time, end_time=end_time)
                elif strategy_name == 'stochastic_rsi':
                    df = fetch_stoch_rsi(symbol, interval, start_time=start_time, end_time=end_time)
                elif strategy_name == 'advanced_momentum':
                    df = fetch_advanced_momentum(symbol, interval, start_time=start_time, end_time=end_time)
                else:
                    continue
                
                strategy_data[strategy_name] = {
                    'df': df,
                    'weight': weight
                }
                
            except Exception as e:
                print(f"Warning: Could not fetch {strategy_name}: {e}")
                continue
        
        if not strategy_data:
            raise ValueError("No strategy data available")
        
        # Merge all strategy signals
        base_df = None
        for strategy_name, data in strategy_data.items():
            df = data['df']
            
            # Ensure required columns exist
            if 'signal' not in df.columns:
                df['signal'] = 'WAIT'
            if 'signal_strength' not in df.columns:
                df['signal_strength'] = 0.5
            
            # Select relevant columns
            cols = ['timestamp', 'close', 'signal', 'signal_strength']
            temp_df = df[cols].copy()
            temp_df = temp_df.rename(columns={
                'signal': f'signal_{strategy_name}',
                'signal_strength': f'strength_{strategy_name}'
            })
            
            if base_df is None:
                base_df = temp_df
            else:
                base_df = base_df.merge(temp_df, on=['timestamp', 'close'], how='outer')
        
        # Calculate ensemble score
        base_df['ensemble_score'] = 0.0
        
        for strategy_name, data in strategy_data.items():
            weight = data['weight']
            signal_col = f'signal_{strategy_name}'
            strength_col = f'strength_{strategy_name}'
            
            if signal_col in base_df.columns:
                # Convert signals to numeric
                signal_numeric = base_df[signal_col].map({'BUY': 1, 'SELL': -1, 'WAIT': 0})
                strength = base_df[strength_col].fillna(0.5)
                
                # Add weighted contribution
                base_df['ensemble_score'] += signal_numeric * strength * weight
        
        # Detect market regime for each row
        base_df['regime'] = 'ranging'  # Default
        if len(base_df) > 50:
            for i in range(50, len(base_df)):
                regime_df = base_df.iloc[max(0, i-50):i+1]
                base_df.loc[i, 'regime'] = self.detect_market_regime(regime_df)
        
        # Generate final signals based on regime-adjusted thresholds
        base_df['signal'] = 'WAIT'
        
        for regime, threshold in self.regime_thresholds.items():
            regime_mask = base_df['regime'] == regime
            base_df.loc[regime_mask & (base_df['ensemble_score'] > threshold), 'signal'] = 'BUY'
            base_df.loc[regime_mask & (base_df['ensemble_score'] < -threshold), 'signal'] = 'SELL'
        
        # Calculate signal strength
        base_df['signal_strength'] = np.abs(base_df['ensemble_score'])
        base_df['signal_strength'] = np.clip(base_df['signal_strength'], 0.1, 1.0)
        
        return base_df[['timestamp', 'close', 'signal', 'signal_strength', 'ensemble_score', 'regime']]
    
    def run_backtest(self, symbol: str, interval: str,
                    start_date: datetime, end_date: datetime,
                    config_name: str = 'balanced') -> Dict:
        """
        Run comprehensive backtest with enhanced features
        """
        print(f"🚀 Running enhanced backtest for {symbol}...")
        
        # Generate ensemble signals
        signals_df = self.generate_ensemble_signals(
            symbol, interval, start_date, end_date, config_name
        )
        
        # Run backtest with enhanced backtester
        backtester = EnhancedBacktester(
            initial_capital=self.initial_capital,
            maker_fee=0.001,  # 0.1% maker fee
            taker_fee=0.001,  # 0.1% taker fee
            slippage_factor=0.0005  # 0.05% slippage
        )
        
        results = backtester.backtest_strategy(signals_df, symbol)
        
        # Add additional analysis
        results['config_used'] = config_name
        results['signal_distribution'] = signals_df['signal'].value_counts().to_dict()
        results['regime_distribution'] = signals_df['regime'].value_counts().to_dict()
        
        return results
    
    def optimize_strategy(self, symbol: str, interval: str,
                         start_date: datetime, end_date: datetime) -> Dict:
        """
        Optimize strategy configuration
        """
        print(f"🔧 Optimizing strategy for {symbol}...")
        
        best_config = None
        best_score = -np.inf
        results = {}
        
        # Test different configurations
        for config_name in self.strategy_configs.keys():
            print(f"  Testing {config_name} configuration...")
            
            try:
                backtest_results = self.run_backtest(
                    symbol, interval, start_date, end_date, config_name
                )
                
                # Calculate optimization score
                score = self._calculate_config_score(backtest_results)
                
                results[config_name] = {
                    'results': backtest_results,
                    'score': score
                }
                
                if score > best_score:
                    best_score = score
                    best_config = config_name
                    
            except Exception as e:
                print(f"    Error with {config_name}: {e}")
                results[config_name] = {'error': str(e), 'score': -1000}
        
        return {
            'best_config': best_config,
            'best_score': best_score,
            'all_results': results
        }
    
    def _calculate_config_score(self, results: Dict) -> float:
        """
        Calculate configuration score
        """
        if 'error' in results or results.get('num_trades', 0) < 5:
            return -1000
        
        total_return = results.get('total_return', 0)
        sharpe_ratio = results.get('sharpe_ratio', 0)
        max_drawdown = results.get('max_drawdown', 1)
        win_rate = results.get('win_rate', 0)
        
        # Composite score
        score = (
            total_return * 100 +
            sharpe_ratio * 50 +
            win_rate * 30 -
            max_drawdown * 200
        )
        
        return score
    
    def generate_report(self, results: Dict) -> str:
        """
        Generate comprehensive performance report
        """
        report = []
        report.append("=" * 60)
        report.append("ULTIMATE TRADING SYSTEM - PERFORMANCE REPORT")
        report.append("=" * 60)
        
        if 'error' in results:
            report.append(f"❌ Error: {results['error']}")
            return "\n".join(report)
        
        # Basic metrics
        report.append(f"\n📊 PERFORMANCE METRICS:")
        report.append(f"Total Return:     {results.get('total_return', 0):8.2%}")
        report.append(f"Sharpe Ratio:     {results.get('sharpe_ratio', 0):8.2f}")
        report.append(f"Max Drawdown:     {results.get('max_drawdown', 0):8.2%}")
        report.append(f"Volatility:       {results.get('volatility', 0):8.2%}")
        
        # Trade statistics
        report.append(f"\n📈 TRADE STATISTICS:")
        report.append(f"Total Trades:     {results.get('num_trades', 0):8d}")
        report.append(f"Win Rate:         {results.get('win_rate', 0):8.1%}")
        report.append(f"Profit Factor:    {results.get('profit_factor', 0):8.2f}")
        report.append(f"Expectancy:       ${results.get('expectancy', 0):7.2f}")
        
        # Signal distribution
        if 'signal_distribution' in results:
            report.append(f"\n🎯 SIGNAL DISTRIBUTION:")
            for signal, count in results['signal_distribution'].items():
                report.append(f"{signal:12}: {count:6d}")
        
        # Regime distribution
        if 'regime_distribution' in results:
            report.append(f"\n🌊 MARKET REGIME DISTRIBUTION:")
            for regime, count in results['regime_distribution'].items():
                report.append(f"{regime:12}: {count:6d}")
        
        # Risk metrics
        if 'risk_metrics' in results:
            risk = results['risk_metrics']
            report.append(f"\n⚠️  RISK METRICS:")
            report.append(f"Current Drawdown: {risk.get('current_drawdown', 0):8.2%}")
            report.append(f"Consecutive Losses: {risk.get('consecutive_losses', 0):6d}")
            report.append(f"Risk Reduction:   {risk.get('risk_reduction_factor', 1):8.2f}")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)

def main():
    """
    Main function to run the ultimate trading system
    """
    print("🚀 ULTIMATE CRYPTO TRADING SYSTEM")
    print("=" * 50)
    
    # Configuration - More data for better signals
    symbol = "BTCUSDT"
    interval = "15m"
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=120)  # 4 months of data for better signals
    
    # Initialize system
    trading_system = UltimateTradingSystem(
        initial_capital=10000,
        risk_per_trade=0.015,  # 1.5% risk per trade
        max_positions=3
    )
    
    try:
        # 1. Optimize configuration
        print(f"\n🔧 OPTIMIZATION PHASE")
        optimization_results = trading_system.optimize_strategy(
            symbol, interval, start_date, end_date
        )
        
        best_config = optimization_results['best_config']
        print(f"✅ Best configuration: {best_config}")
        
        # 2. Run final backtest with best configuration
        print(f"\n📊 FINAL BACKTEST")
        final_results = trading_system.run_backtest(
            symbol, interval, start_date, end_date, best_config
        )
        
        # 3. Generate and display report
        report = trading_system.generate_report(final_results)
        print(report)
        
        # 4. Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Save detailed results
        results_summary = {
            'timestamp': timestamp,
            'symbol': symbol,
            'interval': interval,
            'period': f"{start_date.date()} to {end_date.date()}",
            'optimization': optimization_results,
            'final_results': final_results,
            'best_config': best_config
        }
        
        print(f"\n💾 Results saved with timestamp: {timestamp}")
        print(f"🎯 Ready for live trading with {best_config} configuration!")
        
        return results_summary
        
    except Exception as e:
        print(f"❌ Error in main execution: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    results = main()
