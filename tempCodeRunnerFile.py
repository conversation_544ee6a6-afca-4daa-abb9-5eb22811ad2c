# run_live_loop.py
import time
import pandas as pd
from datetime import datetime, timedelta, timezone
from binance.client import Client

from config.env import BINANCE_API_KEY, BINANCE_SECRET_KEY
from utils.telegram_notify import send_telegram_message

from strategies.UPDATED_bollinger import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd import fetch_rsi_macd
from strategies.supertrend import fetch_supertrend
from strategies.stochastic_rsi import fetch_stoch_rsi

SIGNAL_SCORE = {"BUY": 1, "WAIT": 0, "SELL": -1}
STRATEGY_WEIGHTS = {
    "bollinger": 0.25,
    "ema_crossover": 0.15,
    "rsi_macd": 0.25,
    "stochastic_rsi": 0.20,
    "supertrend": 0.15,
}


def get_btc_price():
    client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)
    ticker = client.get_symbol_ticker(symbol="BTCUSDT")
    return float(ticker["price"])


def fetch_live_signals(symbol="BTCUSDT", interval="15m"):
    now = datetime.utcnow()
    past = now - timedelta(days=1)

    start_str = past.strftime("%d %b %Y %H:%M:%S")
    end_str = now.strftime("%d %b %Y %H:%M:%S")

    print(f"🧭 Start: {start_str} | End: {end_str}")

    return {
        "bollinger": fetch_bollinger(symbol, interval, start_str, end_str),
        "ema_crossover": fetch_ema_crossover(symbol, interval, start_str, end_str),
        "rsi_macd": fetch_rsi_macd(symbol, interval, start_str, end_str),
        "supertrend": fetch_supertrend(symbol, interval, start_str, end_str),
        "stochastic_rsi": fetch_stoch_rsi(symbol, interval, start_str, end_str),
    }



def generate_final_signal(signals: dict):
    df = None
    for name, strat_df in signals.items():
        strat_df = strat_df[["timestamp", "signal"]].copy()
        strat_df.rename(columns={"signal": f"signal_{name}"}, inplace=True)
        strat_df[f"score_{name}"] = strat_df[f"signal_{name}"].map(SIGNAL_SCORE)

        if df is None:
            df = strat_df
        else:
            df = pd.merge(df, strat_df, on="timestamp", how="outer")

    df.sort_values("timestamp", inplace=True)
    df.ffill(inplace=True)
    df.bfill(inplace=True)

    for name in STRATEGY_WEIGHTS:
        col = f"score_{name}"
        if col not in df:
            df[col] = 0

    score_cols = [f"score_{name}" for name in STRATEGY_WEIGHTS]
    df["weighted_score"] = sum(df[col] * STRATEGY_WEIGHTS[name] for name, col in zip(STRATEGY_WEIGHTS, score_cols))

    df["final_signal"] = df["weighted_score"].apply(lambda x: "BUY" if x > 0.2 else "SELL" if x < -0.2 else "WAIT")

    return df.iloc[-1], df  # En son sinyal satırı ve tüm df


def run_loop():
    while True:
        try:
            print("🔁 Yeni sinyal aranıyor...")

            signals = fetch_live_signals()
            latest_row, df = generate_final_signal(signals)
            price = get_btc_price()
            now_str = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            detail = "\n".join([
                f"📉 {name.upper()}: {latest_row[f'signal_{name}']}"
                for name in STRATEGY_WEIGHTS
            ])

            final_signal = latest_row["final_signal"]
            score = latest_row["weighted_score"]

            message = (
                f"🕒 {now_str}\n"
                f"💰 BTC/USDT: ${price:.2f}\n\n"
                f"{detail}\n\n"
                f"📊 Final Signal: {final_signal} ({score:.2f})"
            )

            send_telegram_message(message)

        except Exception as e:
            send_telegram_message(f"⚠️ Hata oluştu: {e}")
            print("⛔ Hata:", e)

        print("⏱️ 5 dakika bekleniyor...\n")
        time.sleep(300)  # 5 dakika

if __name__ == "__main__":
    run_loop()
