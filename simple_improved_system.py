#!/usr/bin/env python3
"""
Simple Improved Trading System
Fixed version with working parameters for immediate use
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta, timezone
from enhanced_backtester import EnhancedBacktester

# Import strategies directly
try:
    from strategies.UPDATED_rsi_macd import fetch_rsi_macd
    from strategies.UPDATED_ema_crossover import fetch_ema_crossover
    from strategies.UPDATED_bollinger import fetch_bollinger
    from strategies.supertrend import fetch_supertrend
    from strategies.stochastic_rsi import fetch_stoch_rsi
    from strategies.advanced_momentum_strategy import fetch_advanced_momentum
except ImportError as e:
    print(f"Warning: Some strategies not available: {e}")

def create_simple_ensemble(symbol: str, interval: str, start_time: datetime, end_time: datetime) -> pd.DataFrame:
    """
    Create a simple but effective ensemble strategy
    """
    print(f"🔄 Fetching data for {symbol}...")
    
    # Fetch data from working strategies
    strategies_data = {}
    
    try:
        strategies_data['bollinger'] = fetch_bollinger(symbol, interval, start_time=start_time, end_time=end_time)
        print("  ✅ Bollinger Bands data fetched")
    except Exception as e:
        print(f"  ❌ Bollinger error: {e}")
    
    try:
        strategies_data['rsi_macd'] = fetch_rsi_macd(symbol, interval, start_time=start_time, end_time=end_time)
        print("  ✅ RSI+MACD data fetched")
    except Exception as e:
        print(f"  ❌ RSI+MACD error: {e}")
    
    try:
        strategies_data['ema_crossover'] = fetch_ema_crossover(symbol, interval, start_time=start_time, end_time=end_time)
        print("  ✅ EMA Crossover data fetched")
    except Exception as e:
        print(f"  ❌ EMA Crossover error: {e}")
    
    if not strategies_data:
        raise ValueError("No strategy data available")
    
    # Merge strategies
    base_df = None
    weights = {'bollinger': 0.4, 'rsi_macd': 0.4, 'ema_crossover': 0.2}
    
    for strategy_name, df in strategies_data.items():
        if 'signal' not in df.columns:
            df['signal'] = 'WAIT'
        if 'signal_strength' not in df.columns:
            df['signal_strength'] = 0.5
        
        # Select relevant columns
        cols = ['timestamp', 'close', 'signal', 'signal_strength']
        temp_df = df[cols].copy()
        temp_df = temp_df.rename(columns={
            'signal': f'signal_{strategy_name}',
            'signal_strength': f'strength_{strategy_name}'
        })
        
        if base_df is None:
            base_df = temp_df
        else:
            base_df = base_df.merge(temp_df, on=['timestamp', 'close'], how='outer')
    
    # Calculate ensemble score
    base_df['ensemble_score'] = 0.0
    
    for strategy_name in strategies_data.keys():
        weight = weights.get(strategy_name, 0.33)
        signal_col = f'signal_{strategy_name}'
        strength_col = f'strength_{strategy_name}'
        
        if signal_col in base_df.columns:
            signal_numeric = base_df[signal_col].map({'BUY': 1, 'SELL': -1, 'WAIT': 0})
            strength = base_df[strength_col].fillna(0.5)
            base_df['ensemble_score'] += signal_numeric * strength * weight
    
    # Generate final signals with optimal threshold
    base_df['signal'] = 'WAIT'
    threshold = getattr(globals(), 'OPTIMAL_THRESHOLD', 0.12)  # Use optimal threshold if available
    
    base_df.loc[base_df['ensemble_score'] > threshold, 'signal'] = 'BUY'
    base_df.loc[base_df['ensemble_score'] < -threshold, 'signal'] = 'SELL'
    
    # Calculate signal strength
    base_df['signal_strength'] = np.abs(base_df['ensemble_score'])
    base_df['signal_strength'] = np.clip(base_df['signal_strength'], 0.1, 1.0)
    
    return base_df[['timestamp', 'close', 'signal', 'signal_strength', 'ensemble_score']]

def run_simple_backtest(symbol: str = "BTCUSDT", interval: str = "15m", days_back: int = 90) -> dict:
    """
    Run a simple but effective backtest
    """
    print(f"🚀 Running Simple Improved Backtest")
    print("=" * 50)
    
    # Date range
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days_back)
    
    print(f"Period: {start_date.date()} to {end_date.date()}")
    print(f"Symbol: {symbol}, Interval: {interval}")
    
    # Generate signals
    signals_df = create_simple_ensemble(symbol, interval, start_date, end_date)
    
    print(f"\n📊 Signal Summary:")
    signal_counts = signals_df['signal'].value_counts()
    for signal, count in signal_counts.items():
        print(f"  {signal}: {count} ({count/len(signals_df)*100:.1f}%)")
    
    # Run backtest
    print(f"\n🔄 Running backtest...")
    backtester = EnhancedBacktester(
        initial_capital=10000,
        maker_fee=0.001,
        taker_fee=0.001,
        slippage_factor=0.0005
    )
    
    results = backtester.backtest_strategy(signals_df, symbol)
    
    return results

def test_different_thresholds(symbol: str = "BTCUSDT", interval: str = "15m", days_back: int = 60):
    """
    Test different signal thresholds to find optimal settings
    """
    print(f"🔧 Testing Different Signal Thresholds")
    print("=" * 50)
    
    end_date = datetime.now(timezone.utc)
    start_date = end_date - timedelta(days=days_back)
    
    # Get base signals
    base_signals = create_simple_ensemble(symbol, interval, start_date, end_date)
    
    # Test different thresholds
    thresholds = [0.05, 0.08, 0.10, 0.12, 0.15, 0.20]
    results = {}
    
    for threshold in thresholds:
        print(f"  Testing threshold: {threshold}")
        
        # Apply threshold
        test_df = base_signals.copy()
        test_df['signal'] = 'WAIT'
        test_df.loc[test_df['ensemble_score'] > threshold, 'signal'] = 'BUY'
        test_df.loc[test_df['ensemble_score'] < -threshold, 'signal'] = 'SELL'
        
        # Count signals
        signal_counts = test_df['signal'].value_counts()
        total_signals = signal_counts.get('BUY', 0) + signal_counts.get('SELL', 0)
        
        if total_signals < 3:
            print(f"    Too few signals ({total_signals}), skipping")
            continue
        
        # Run backtest
        backtester = EnhancedBacktester(initial_capital=10000)
        backtest_results = backtester.backtest_strategy(test_df, symbol)
        
        # Calculate score
        total_return = backtest_results.get('total_return', 0)
        win_rate = backtest_results.get('win_rate', 0)
        num_trades = backtest_results.get('num_trades', 0)
        max_drawdown = backtest_results.get('max_drawdown', 0)
        
        score = total_return * 100 + win_rate * 50 - max_drawdown * 100
        
        results[threshold] = {
            'return': total_return,
            'win_rate': win_rate,
            'num_trades': num_trades,
            'max_drawdown': max_drawdown,
            'score': score
        }
        
        print(f"    Return: {total_return:.2%}, Trades: {num_trades}, Win Rate: {win_rate:.1%}")
    
    # Find best threshold
    if results:
        best_threshold = max(results.keys(), key=lambda k: results[k]['score'])
        print(f"\n✅ Best Threshold: {best_threshold}")
        print(f"   Performance: {results[best_threshold]}")
        return best_threshold, results
    else:
        print("❌ No valid results found")
        return 0.12, {}

def main():
    """
    Main function to run the improved system
    """
    print("🚀 SIMPLE IMPROVED CRYPTO TRADING SYSTEM")
    print("=" * 60)
    
    # Test 1: Find optimal threshold
    print("\n" + "="*30 + " PHASE 1: OPTIMIZATION " + "="*30)
    best_threshold, threshold_results = test_different_thresholds()
    
    # Test 2: Run full backtest with optimal threshold
    print("\n" + "="*30 + " PHASE 2: FULL BACKTEST " + "="*30)
    
    # Modify the create_simple_ensemble function to use best threshold
    global OPTIMAL_THRESHOLD
    OPTIMAL_THRESHOLD = best_threshold
    
    results = run_simple_backtest(days_back=120)  # 4 months
    
    # Display results
    print("\n" + "=" * 60)
    print("FINAL RESULTS")
    print("=" * 60)
    
    if 'error' not in results:
        print(f"\n📊 Performance Metrics:")
        print(f"Total Return:     {results.get('total_return', 0):8.2%}")
        print(f"Sharpe Ratio:     {results.get('sharpe_ratio', 0):8.2f}")
        print(f"Max Drawdown:     {results.get('max_drawdown', 0):8.2%}")
        print(f"Volatility:       {results.get('volatility', 0):8.2%}")
        
        print(f"\n📈 Trade Statistics:")
        print(f"Total Trades:     {results.get('num_trades', 0):8d}")
        print(f"Win Rate:         {results.get('win_rate', 0):8.1%}")
        print(f"Profit Factor:    {results.get('profit_factor', 0):8.2f}")
        print(f"Expectancy:       ${results.get('expectancy', 0):7.2f}")
        
        # Recommendation
        total_return = results.get('total_return', 0)
        win_rate = results.get('win_rate', 0)
        num_trades = results.get('num_trades', 0)
        
        print(f"\n🎯 Recommendation:")
        if total_return > 0.02 and win_rate > 0.4 and num_trades >= 10:
            print("🚀 Excellent! Ready for live trading with small positions.")
        elif total_return > 0 and win_rate > 0.35 and num_trades >= 5:
            print("✅ Good performance. Start with paper trading.")
        elif num_trades >= 5:
            print("⚠️ Marginal performance. Paper trade first, then optimize.")
        else:
            print("❌ Needs more optimization before live trading.")
        
        print(f"\n💡 Optimal Signal Threshold: {best_threshold}")
        print(f"🎯 Use this threshold in your live trading system!")
        
    else:
        print(f"❌ Error in backtest: {results.get('error', 'Unknown error')}")
    
    return results

# Global variable for optimal threshold
OPTIMAL_THRESHOLD = 0.12

if __name__ == "__main__":
    results = main()
