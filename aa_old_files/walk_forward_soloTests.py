#!/usr/bin/env python3
import os
import numpy as np
import pandas as pd
from datetime import datetime, timezone
from strategies.UPDATED_bollinger     import fetch_bollinger
from strategies.UPDATED_ema_crossover import fetch_ema_crossover
from strategies.UPDATED_rsi_macd      import fetch_rsi_macd
from strategies.supertrend            import fetch_supertrend
from strategies.stochastic_rsi        import fetch_stoch_rsi

# ── Parameters ───────────────────────────────────────────────────────────────────
symbol        = "BTCUSDT"
interval      = "15m"
start_time    = datetime(2023, 1, 1, tzinfo=timezone.utc)
end_time      = datetime(2023, 7, 1, tzinfo=timezone.utc)

init_cap      = 1000.0
fee_rate      = 0.001    # 0.1% per trade
slippage      = 0.001    # 0.1% slippage
stop_atr_mult = 1.0      # ATR‑based stop‑loss multiplier
take_atr_mult = 2.0      # ATR‑based take‑profit multiplier
max_trades    = 100      # cap for solo tests

# ── Helper <PERSON>ctions ────────────────────────────────────────────────────────────
def compute_atr(closes, period=14):
    tr = closes.diff().abs()
    return tr.rolling(window=period, min_periods=1).mean()

def backtest_atr(signals, init_balance, fee_rate, slip, stop_mult, take_mult, max_trades):
    """
    signals: DataFrame with ['timestamp','close','signal']
    """
    df = signals.reset_index(drop=True)
    df["atr"] = compute_atr(df["close"], period=14)

    bal, pos, entry = init_balance, 0.0, 0.0
    sl, tp = 0.0, 0.0
    trades, in_trade = [], False

    for _, row in df.iterrows():
        price, sig, atr = row["close"], row["signal"], row["atr"]

        # ENTRY
        if not in_trade and sig == "BUY" and len(trades) < max_trades:
            exec_price = price * (1 + slip)
            qty        = bal / (exec_price * (1 + fee_rate))
            cost       = qty * exec_price
            commission = cost * fee_rate
            if cost + commission <= bal:
                bal      -= (cost + commission)
                pos       = qty
                entry     = exec_price
                sl        = entry - stop_mult * atr
                tp        = entry + take_mult * atr
                in_trade  = True

        # EXIT
        elif in_trade:
            exit_price = None
            if price <= sl:
                exit_price = sl * (1 - slip)
            elif price >= tp:
                exit_price = tp * (1 - slip)
            elif sig == "SELL":
                exit_price = price * (1 - slip)

            if exit_price is not None:
                proceeds   = pos * exit_price
                commission = proceeds * fee_rate
                pnl        = proceeds - commission - (pos * entry)
                bal       += (proceeds - commission)
                trades.append(pnl)
                pos, in_trade = 0.0, False

        if len(trades) >= max_trades:
            break

    # equity curve
    equity = []
    for price in df["close"]:
        equity.append(bal + (pos * price if in_trade else 0))
    return equity, trades

def compute_metrics(equity, initial, interval):
    arr   = np.array(equity)
    rets  = arr[1:] / arr[:-1] - 1
    bars  = 1440 / int(interval[:-1])  # e.g. "15m"
    daily = rets * bars
    mu    = np.nanmean(daily)
    sd    = np.nanstd(daily)
    sharpe= (mu * 252) / (sd * np.sqrt(252)) if sd else np.nan
    total = arr[-1] / initial - 1
    dd    = np.min((arr - np.maximum.accumulate(arr)) / np.maximum.accumulate(arr))
    return total, sharpe, dd

def trade_stats(pnls):
    if not pnls:
        return dict(n_trades=0, win_rate=np.nan, avg_win=np.nan,
                    avg_loss=np.nan, expectancy=np.nan)
    arr    = np.array(pnls)
    wins   = arr[arr>0]; losses = arr[arr<0]
    wr     = len(wins) / len(arr)
    aw     = wins.mean()    if wins.size   else 0
    al     = losses.mean()  if losses.size else 0
    exp    = wr*aw + (1-wr)*al
    return dict(n_trades=len(arr), win_rate=wr,
                avg_win=aw, avg_loss=al, expectancy=exp)

# ── Strategy Suite ──────────────────────────────────────────────────────────────
strategies = {
    "bollinger":     fetch_bollinger,
    "ema_crossover": fetch_ema_crossover,
    "rsi_macd":      fetch_rsi_macd,
    "supertrend":    fetch_supertrend,
    "stoch_rsi":     fetch_stoch_rsi
}

# ── Run Solo Strategy Tests ─────────────────────────────────────────────────────
if __name__ == "__main__":
    results = []
    for name, fetch_fn in strategies.items():
        print(f"\n=== Testing {name} ===")
        df = fetch_fn(
            symbol      = symbol,
            interval    = interval,
            start_time  = start_time,
            end_time    = end_time
        )
        # require ['timestamp','signal','close']
        signals = df[["timestamp","signal","close"]]

        equity, trades = backtest_atr(
            signals,
            init_balance = init_cap,
            fee_rate     = fee_rate,
            slip         = slippage,
            stop_mult    = stop_atr_mult,
            take_mult    = take_atr_mult,
            max_trades   = max_trades
        )

        total, sharpe, dd = compute_metrics(equity, init_cap, interval)
        stats = trade_stats(trades)

        print(f"Trades:     {stats['n_trades']}")
        print(f"Win rate:   {stats['win_rate']:.2%}")
        print(f"Expectancy: {stats['expectancy']:.4f}")
        print(f"Return:     {total:.2%}")
        print(f"Sharpe:     {sharpe:.2f}")
        print(f"Max DD:     {dd:.2%}")

        results.append({
            "strategy":   name,
            "n_trades":   stats["n_trades"],
            "win_rate":   stats["win_rate"],
            "expectancy": stats["expectancy"],
            "return":     total,
            "sharpe":     sharpe,
            "max_dd":     dd
        })

    summary = pd.DataFrame(results)
    print("\n=== Summary ===")
    print(summary.to_string(index=False))
