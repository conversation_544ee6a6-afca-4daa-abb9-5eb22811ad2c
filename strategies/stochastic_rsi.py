# stochastic_rsi.py
import pandas as pd
import numpy as np
from binance.client import Client
from dotenv import load_dotenv
import os
from datetime import datetime, timedelta

# ENV
load_dotenv()
client = Client(os.getenv("BINANCE_API_KEY"), os.getenv("BINANCE_SECRET_KEY"))

def fetch_ohlcv(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=None, end_time=None):
    if end_time is None:
        end_time = datetime.utcnow()
    if start_time is None:
        start_time = end_time - timedelta(days=90)

    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(symbol=symbol, interval=interval, startTime=current_start, endTime=end_ts)
        if not klines:
            break
        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        # cast all OHLC + volume
        for col in ["open","high","low","close","volume"]:
            df[col] = df[col].astype(float)
     
        # keep timestamp + full OHLC/volume so regime detector works
        df_list.append(df[["timestamp","open","high","low","close","volume"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def compute_stochastic_rsi(df, rsi_period=14, stoch_period=14, smooth_k=3, smooth_d=3):
    df = df.copy()
    delta = df["close"].diff()
    gain = np.where(delta > 0, delta, 0)
    loss = np.where(delta < 0, -delta, 0)

    avg_gain = pd.Series(gain).rolling(window=rsi_period).mean()
    avg_loss = pd.Series(loss).rolling(window=rsi_period).mean()
    rs = avg_gain / avg_loss
    rsi = 100 - (100 / (1 + rs))

    stoch_rsi = (rsi - rsi.rolling(stoch_period).min()) / (rsi.rolling(stoch_period).max() - rsi.rolling(stoch_period).min())
    k = stoch_rsi.rolling(smooth_k).mean()
    d = k.rolling(smooth_d).mean()

    df["stoch_rsi_k"] = k
    df["stoch_rsi_d"] = d
    return df

def generate_stoch_rsi_signals(df):
    df = compute_stochastic_rsi(df)
    df["signal"] = "WAIT"
    df.loc[(df["stoch_rsi_k"] > df["stoch_rsi_d"]) & (df["stoch_rsi_k"].shift(1) <= df["stoch_rsi_d"].shift(1)) & (df["stoch_rsi_k"] < 0.3), "signal"] = "BUY"
    df.loc[(df["stoch_rsi_k"] < df["stoch_rsi_d"]) & (df["stoch_rsi_k"].shift(1) >= df["stoch_rsi_d"].shift(1)) & (df["stoch_rsi_k"] > 0.7), "signal"] = "SELL"
    
    df["score_stochastic_rsi"] = df["signal"].map({"BUY": 1, "SELL": -1, "WAIT": 0})

    return df

def fetch_stoch_rsi(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, start_time=None, end_time=None):
    df = fetch_ohlcv(symbol=symbol, interval=interval, start_time=start_time, end_time=end_time)
    return generate_stoch_rsi_signals(df)

if __name__ == "__main__":
    df = fetch_stoch_rsi()
    print(df[["timestamp", "close", "stoch_rsi_k", "stoch_rsi_d", "signal"]].tail(20))
