import pandas as pd
import numpy as np
import os
import matplotlib.pyplot as plt
from dotenv import load_dotenv
from binance.client import Client
from datetime import datetime, timedelta

# ENV yükle
load_dotenv()
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY")
BINANCE_SECRET_KEY = os.getenv("BINANCE_SECRET_KEY")

client = Client(BINANCE_API_KEY, BINANCE_SECRET_KEY)

def fetch_ohlcv(symbol="BTCUSDT", interval=Client.KLINE_INTERVAL_15MINUTE, lookback_days=90):
    end_time = datetime.utcnow()
    start_time = end_time - timedelta(days=lookback_days)

    start_ts = int(start_time.timestamp() * 1000)
    end_ts = int(end_time.timestamp() * 1000)

    df_list = []
    current_start = start_ts

    while current_start < end_ts:
        klines = client.get_klines(
            symbol=symbol,
            interval=interval,
            startTime=current_start,
            endTime=end_ts
        )

        if not klines:
            break

        df = pd.DataFrame(klines, columns=[
            "timestamp", "open", "high", "low", "close", "volume",
            "close_time", "quote_asset_volume", "number_of_trades",
            "taker_buy_base", "taker_buy_quote", "ignore"
        ])
        df["timestamp"] = pd.to_datetime(df["timestamp"], unit='ms')
        df["close"] = df["close"].astype(float)
        df_list.append(df[["timestamp", "close"]])
        current_start = int(df["timestamp"].iloc[-1].timestamp() * 1000) + 1

    return pd.concat(df_list, ignore_index=True)

def generate_ema_crossover_signals(df, short_ema=50, long_ema=200):
    df["ema_short"] = df["close"].ewm(span=short_ema, adjust=False).mean()
    df["ema_long"] = df["close"].ewm(span=long_ema, adjust=False).mean()
    df["signal"] = "WAIT"

    df.loc[df["ema_short"] > df["ema_long"], "signal"] = "BUY"
    df.loc[df["ema_short"] < df["ema_long"], "signal"] = "SELL"
    
    return df

def simulate_trades(df, initial_balance=1000, trading_fee=0.001):
    balance = initial_balance
    position = 0
    log_lines = []
    portfolio = []
    last_signal = "WAIT"

    for _, row in df.iterrows():
        signal = row["signal"]
        price = row["close"]
        time = row["timestamp"]

        if signal == "BUY" and last_signal != "BUY" and balance > 0:
            position = (balance * (1 - trading_fee)) / price
            balance = 0
            log_lines.append(f"{time} - BUY at ${price:.2f}")

        elif signal == "SELL" and last_signal != "SELL" and position > 0:
            balance = (position * price) * (1 - trading_fee)
            position = 0
            log_lines.append(f"{time} - SELL at ${price:.2f}")
        else:
            log_lines.append(f"{time} - WAIT at ${price:.2f}")

        last_signal = signal
        portfolio_value = balance + (position * price)
        portfolio.append(portfolio_value)

    df["portfolio_value"] = portfolio

    with open("backtest_log_ema_crossover.txt", "w") as f:
        f.write("\n".join(log_lines))

    return df, portfolio[-1], max(portfolio), min(portfolio)

# === RUN ===
df = fetch_ohlcv()
df = generate_ema_crossover_signals(df)
df, final_value, max_value, min_value = simulate_trades(df)

print(f"\n💼 Final Portfolio Value: ${final_value:.2f}")
print(f"📈 Max Portfolio Value: ${max_value:.2f}")
print(f"📉 Min Portfolio Value: ${min_value:.2f}")

# Plot
plt.figure(figsize=(14, 6))
plt.plot(df["timestamp"], df["portfolio_value"], label="Portfolio Value", color="blue")
plt.xlabel("Time")
plt.ylabel("Portfolio Value ($)")
plt.title("📊 EMA Crossover Strategy Backtest")
plt.grid(True)
plt.legend()
plt.tight_layout()
plt.savefig("portfolio_plot_ema_crossover.png")
plt.show()
